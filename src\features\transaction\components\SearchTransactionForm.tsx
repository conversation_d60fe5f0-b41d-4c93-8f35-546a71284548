import SearchForm from 'components/partials/SearchForm';
import { useTranslation } from 'react-i18next';
import { SearchField } from 'types/common/Search';
import { convertConstantToSelectOptions, convertSelectToStringKey } from 'utils/common';
import { TransactionStatusNames, TransactionTypeNames } from '../../../types/Transaction';

interface IProps {
    isLoading: boolean;
}

export default function SearchTransactionForm({ isLoading }: Readonly<IProps>) {
    const { t } = useTranslation();

    const fields: SearchField[] = [
        //{ name: 'customer_id', type: 'text', label: t('transaction.customer'), wrapClassName: 'col-md-4 col-12' },
        //{ name: 'plan_id', type: 'text', label: t('transaction.plan'), wrapClassName: 'col-md-4 col-12' },
        {
            name: 'type_id',
            type: 'select',
            label: 'Transaction Type',
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: true,
                choices: convertSelectToStringKey(convertConstantToSelectOptions(TransactionTypeNames, t, true)),
            },
        },
        {
            name: 'status_id',
            type: 'select',
            label: t('statusName'),
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: true,
                choices: convertSelectToStringKey(convertConstantToSelectOptions(TransactionStatusNames, t, true)),
            },
        },
    ];

    return <SearchForm fields={fields} isLoading={isLoading} />;
}
