import { TFunction } from 'i18next';
import { find, get, isEmpty } from 'lodash';
import { ToastOptions, toast } from 'react-toastify';
import { ItemParam, ItemParamModel, SelectOption, SelectOptionModel } from 'types/common/Item';
import { DataList, Paging } from '../types/common';

export const showToast = (success: boolean, messages?: string[]) => {
    const options: ToastOptions<{}> = {
        position: 'top-right',
        toastId: Math.random(),
    };
    let MsgNode = null;
    if (messages && messages.length > 1) {
        MsgNode = (
            <div>
                {messages.map((message: string, index: number) => (
                    <p key={index}>{message}</p>
                ))}
            </div>
        );
    }
    if (success) {
        if (!isEmpty(messages)) {
            if (messages!.length === 1) {
                toast.success(messages![0], options);
            } else {
                toast.success(MsgNode, options);
            }
        }
    } else {
        if (!isEmpty(messages)) {
            if (messages!.length === 1) {
                toast.error(messages![0], options);
            } else {
                toast.error(MsgNode, options);
            }
        }
    }
};

export const genTableIndex = (index: number, limit: number, currentPage: number) =>
    index + limit * (currentPage - 1) + 1;

export const getFieldHtml = (
    fields: ItemParam[] | ItemParamModel[],
    id: number | string,
    t: TFunction<'translation', undefined>
) => {
    const item = find(fields, { id }) as ItemParam;
    if (item) {
        return <span className={item.className}>{t(`constants.${item.name}`)}</span>;
    }
    return <></>;
};

export const getFieldInArrayObject = (
    listObj: {}[],
    id: number | string,
    fieldName: string = 'name',
    defaultValue: string = '',
    fieldCompare = 'id'
) => get(find(listObj, { [fieldCompare]: id }), fieldName, defaultValue);

export const toggleModalOpen = (show: boolean) => {
    if (show) {
        document.body.classList.add('modal-open');
    } else {
        document.body.classList.remove('modal-open');
    }
};

export const selectItem = (listItems: ItemParam[], t: TFunction<'translation', undefined>, noNoneOption?: boolean) => {
    const selectOptions: JSX.Element[] = [];
    if (!noNoneOption) {
        selectOptions.push(
            <option key={0} value={0}>
                --
            </option>
        );
    }
    listItems.forEach((item) => {
        selectOptions.push(
            <option key={item.id} value={item.id}>
                {t(`constants.${item.name}`)}
            </option>
        );
    });
    return selectOptions;
};

export const convertConstantToSelectOptions = (
    items: ItemParam[],
    t: TFunction<'translation', undefined>,
    noNoneOption?: boolean
) => {
    const selectOptions: SelectOption[] = [];
    if (!noNoneOption) {
        selectOptions.push({
            value: 0,
            label: '',
        });
    }
    items.forEach((item) => {
        selectOptions.push({
            value: item.id,
            label: t(`constants.${item.name}`),
        });
    });
    return selectOptions;
};

export const convertObjectToSelectOptions = (
    items: ItemParamModel[],
    noNoneOption?: boolean,
    t?: TFunction<'translation', undefined>
) => {
    const selectOptions: SelectOptionModel[] = [];
    if (!noNoneOption) {
        selectOptions.push({
            value: '',
            label: '--',
        });
    }
    items.forEach((item) => {
        selectOptions.push({
            value: item.id,
            label: t ? t(`constants.${item.name}`) : item.name,
        });
    });
    return selectOptions;
};

export const convertSelectToStringKey = (items: SelectOption[]): SelectOptionModel[] =>
    items.map((item) => ({
        value: item.value.toString(),
        label: item.label,
    }));

// tslint:disable-next-line
export const getSelectStyle = (baseStyles: any, state: any, isError: boolean) => ({
    ...baseStyles,
    borderColor: state.isFocused ? (isError ? '#ea5455' : baseStyles.borderColor) : isError ? '#ea5455' : '#d8d6de',
    boxShadow: 'none',
});

export const isValidImageFile = (file: File) => {
    const validImageTypes = [
        'image/png',
        'image/jpg',
        'image/jpeg',
        'image/gif',
        'image/webp',
        'image/bmp',
        'image/svg+xml',
    ];
    return file && validImageTypes.includes(file.type);
};

/* tslint:disable:no-any */
export function flattenObject(obj: any, prefix = ''): Record<string, any> {
    return Object.keys(obj).reduce((acc, k) => {
        const pre = prefix.length ? prefix + '.' : '';
        if (typeof obj[k] === 'object' && obj[k] !== null && !Array.isArray(obj[k])) {
            Object.assign(acc, flattenObject(obj[k], pre + k));
        } else {
            acc[pre + k] = obj[k];
        }
        return acc;
    }, {} as Record<string, any>);
}

export const convertPaging = <T, K extends { page?: string; limit?: string }>(
    data: DataList<T> | undefined,
    paramConfig: K
): Paging => ({
    count_item: data?.totalCount ?? 0,
    total_page: data?.totalPages ?? 0,
    current_page: Number(paramConfig.page),
    limit: Number(paramConfig.limit),
});

export const generateFilters = (params: any, filterConfig: any) => {
    const filters = [];
    for (const paramKey of Object.keys(params)) {
        const value = params[paramKey];
        const config = filterConfig[paramKey];

        if (config && value) {
            const { key, operator } = config;
            filters.push(`${key}:${operator}(${value})`);
        }
    }
    return filters;
};
