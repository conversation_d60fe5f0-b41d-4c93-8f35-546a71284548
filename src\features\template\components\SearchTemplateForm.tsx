import SearchForm from 'components/partials/SearchForm';
import { useTranslation } from 'react-i18next';
import { ItemStatusNames } from 'types/common/Item';
import { SearchField } from 'types/common/Search';
import { convertConstantToSelectOptions, convertSelectToStringKey } from 'utils/common';

interface IProps {
    isLoading: boolean;
}

export default function SearchTemplateForm({ isLoading }: Readonly<IProps>) {
    const { t } = useTranslation();

    const fields: SearchField[] = [
        { name: 'search', type: 'text', label: t('keywords'), wrapClassName: 'col-md-4 col-12' },
        {
            name: 'status_id',
            type: 'select',
            label: t('statusName'),
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: true,
                choices: convertSelectToStringKey(convertConstantToSelectOptions(ItemStatusNames, t, true)),
            },
        },
        // {
        //     name: 'designer_id',
        //     type: 'select',
        //     label: t('designer.multiple'),
        //     wrapClassName: 'col-md-4 col-12',
        //     options: {
        //         multiple: true,
        //         choices: [],
        //     },
        // },
        // {
        //     name: 'industry_id',
        //     type: 'select',
        //     label: t('industry.multiple'),
        //     wrapClassName: 'col-md-4 col-12',
        //     options: {
        //         multiple: true,
        //         choices: [],
        //     },
        // },
    ];

    return <SearchForm fields={fields} isLoading={isLoading} />;
}
