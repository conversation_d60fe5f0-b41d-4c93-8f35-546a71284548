import ContentHeader from 'components/partials/ContentHeader';
import ModalConfirm from 'components/partials/ModalConfirm';
import Spinner from 'components/partials/Spinner';
import { PAGINATION, QUERY_KEY, SUCCESS_MESSAGE } from 'constants/common';
import { useGraphQLMutation } from 'hooks/useGraphQLMutation';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import { useCallback, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import {
    MASTER_TAG_CREATE,
    MASTER_TAG_DELETE,
    MASTER_TAG_IMPORT,
    MASTER_TAG_LIST,
    MASTER_TAG_UPDATE,
} from 'services/MasterTagService';
import MasterTag, { MasterTagImportResult, MasterTagQuery } from 'types/MasterTag';
import { convertPaging, generateFilters, showToast } from 'utils/common';
import { useTranslation } from 'react-i18next';
import ListMasterTag from '../components/ListMasterTag';
import { keepPreviousData } from '@tanstack/react-query';
import isUndefined from 'lodash/isUndefined';
import { omitBy } from 'lodash';
import useQueryParams from 'hooks/useQueryParams';
import PaginationTable from 'components/partials/PaginationTable';
import SearchForm from 'components/partials/SearchForm';
import ModalMasterTagUpdate from '../components/ModalMasterTagUpdate';
import ModalMasterTagImport from '../components/ModalMasterTagImport';
import { baseFilterConfig, BaseSearchParam } from '../../../types/common';
import file from 'services/file';

export default function MasterTagList() {
    const { t } = useTranslation();
    const [showDelete, setShowDelete] = useState(false);
    const [showModal, setShowModal] = useState(false);
    const [showImportModal, setShowImportModal] = useState(false);
    const [selectedMasterTag, setSelectedMasterTag] = useState<MasterTag | undefined>(undefined);
    const [itemId, setItemId] = useState(0);
    const [importResult, setImportResult] = useState<MasterTagImportResult | undefined>(undefined);
    const [isImporting, setIsImporting] = useState(false);

    const { queryParams, setQueryParams } = useQueryParams<BaseSearchParam>();
    const paramConfig: BaseSearchParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            search: queryParams.search,
        },
        isUndefined
    );
    const { search, limit, page, ...dataParamConfig } = paramConfig;
    const filters = generateFilters(dataParamConfig, baseFilterConfig);

    const { data, isLoading, isRefetching, refetch } = useGraphQLQuery<MasterTagQuery>(
        [QUERY_KEY.MASTER_TAGS, paramConfig, filters],
        MASTER_TAG_LIST,
        {
            page: Number(page),
            limit: limit,
            search,
            filters: filters.length > 0 ? filters : undefined,
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );

    const handleShowMsg = useCallback(
        (isDelete: boolean) => {
            if (isDelete) {
                setShowDelete(false);
                setItemId(0);
            } else {
                setShowModal(false);
            }
            showToast(true, [SUCCESS_MESSAGE]);
            refetch();
        },
        [refetch]
    );

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    const deleteMutation = useGraphQLMutation(MASTER_TAG_DELETE, '', {
        onSuccess: () => {
            handleShowMsg(true);
        },
    });

    const createMutation = useGraphQLMutation<{}>(MASTER_TAG_CREATE, '', {
        onSuccess: () => {
            handleShowMsg(false);
        },
    });

    const updateMutation = useGraphQLMutation<{}>(MASTER_TAG_UPDATE, '', {
        onSuccess: () => {
            handleShowMsg(false);
        },
    });

    const handleDelete = (id: number) => {
        setItemId(id);
        setShowDelete(true);
    };

    const deleteItem = () => {
        deleteMutation.mutate({ id: itemId });
    };

    const handleEdit = (id: number) => {
        const masterTag = data?.master_tags_list.data.find((item) => item.id === id);
        setSelectedMasterTag(masterTag);
        setShowModal(true);
    };

    const handleSubmit = (formData: MasterTag) => {
        if (selectedMasterTag?.id) {
            updateMutation.mutate({
                id: selectedMasterTag.id,
                body: {
                    name: formData.name,
                    type: formData.type,
                    display_name: formData.display_name,
                    tool_tip: formData.tool_tip,
                },
            });
        } else {
            createMutation.mutate({
                body: {
                    name: formData.name,
                    type: formData.type,
                    display_name: formData.display_name,
                    tool_tip: formData.tool_tip,
                },
            });
        }
    };

    const handleImportCsv = async (selectedFile: File) => {
        try {
            const formData = new FormData();
            formData.append('file', selectedFile);
            setIsImporting(true);
            const response = await file.post(MASTER_TAG_IMPORT, formData);
            setImportResult(response.data.master_tags_import);
            showToast(true, ['Import completed successfully']);
            refetch();
        } catch (error) {
            // @ts-ignore
            const errorMessage = error.response?.data?.message ?? error.message ?? 'Import failed';
            showToast(false, [errorMessage]);
        } finally {
            setIsImporting(false);
        }
    };

    const handleImportModalClose = (show: boolean) => {
        if (!show && importResult) {
            refetch();
        }
        setShowImportModal(show);
        if (!show) {
            setImportResult(undefined);
        }
    };

    const actionMenu = (_id: number) => {
        if (_id === 1) {
            setSelectedMasterTag(undefined);
            setShowModal(true);
        }
        if (_id === 2) {
            setImportResult(undefined);
            setShowImportModal(true);
        }
    };

    return (
        <>
            <Helmet>
                <title>Master Tag</title>
            </Helmet>
            <ContentHeader
                title="Master Tag"
                contextMenu={[
                    {
                        text: 'Add Master Tag',
                        icon: 'PLUS',
                        id: 1,
                        fnCallBack: { actionMenu },
                    },
                    {
                        text: 'Import Master Tag',
                        icon: 'UPLOAD',
                        id: 2,
                        fnCallBack: { actionMenu },
                    },
                ]}
            />
            <div className="content-body">
                <div className="col-12">
                    <SearchForm
                        fields={[
                            { name: 'search', type: 'text', label: t('keywords'), wrapClassName: 'col-md-4 col-12' },
                        ]}
                        isLoading={isLoading || isRefetching}
                    />
                    {(isLoading || isRefetching) && <Spinner />}
                    {!isLoading && !isRefetching && (
                        <div className="card">
                            <ListMasterTag
                                items={data?.master_tags_list.data ?? []}
                                handleDelete={handleDelete}
                                paging={convertPaging<MasterTag, BaseSearchParam>(data?.master_tags_list, paramConfig)}
                                handleEdit={handleEdit}
                            />
                            <PaginationTable
                                countItem={data?.master_tags_list.totalCount}
                                totalPage={data?.master_tags_list.totalPages}
                                currentPage={data?.master_tags_list.currentPage}
                                handlePageChange={handlePageChange}
                            />
                        </div>
                    )}
                    <ModalConfirm
                        show={showDelete}
                        text={t('confirm.delete', { data: 'master tag' })}
                        btnDisabled={deleteMutation.isPending}
                        changeShow={(s: boolean) => setShowDelete(s)}
                        submitAction={deleteItem}
                    />
                    <ModalMasterTagUpdate
                        show={showModal}
                        masterTag={selectedMasterTag}
                        isLoading={createMutation.isPending || updateMutation.isPending}
                        changeShow={(s: boolean) => setShowModal(s)}
                        submitAction={handleSubmit}
                    />
                    <ModalMasterTagImport
                        show={showImportModal}
                        isLoading={isImporting}
                        changeShow={handleImportModalClose}
                        submitAction={handleImportCsv}
                        importResult={importResult}
                    />
                </div>
            </div>
        </>
    );
}
