import { Edit } from 'react-feather';
import { genTableIndex } from 'utils/common';
import { Paging } from '../../../types/common';
import Config from '../../../types/Config';

interface IProps {
    items: Config[];
    paging: Paging;
    handleEdit: (id: number) => void;
}

export default function ListConfig({ items, paging, handleEdit }: Readonly<IProps>) {
    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="text-center">No</th>
                        <th>Name</th>
                        <th>Code</th>
                        <th>Value</th>
                        <th className="text-center">Action</th>
                    </tr>
                </thead>
                <tbody>
                    {items.map((item, index) => (
                        <tr key={item.id}>
                            <td className="text-center">{genTableIndex(index, paging.limit, paging.current_page)}</td>
                            <td>{item.name}</td>
                            <td>{item.code}</td>
                            <td>{item.value}</td>
                            <td className="text-center">
                                <button
                                    className="btn btn-icon btn-sm btn-flat-primary waves-effect"
                                    onClick={() => handleEdit(item.id!)}
                                >
                                    <Edit size={14} />
                                </button>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
