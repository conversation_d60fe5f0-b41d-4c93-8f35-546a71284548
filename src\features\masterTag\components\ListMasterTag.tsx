import { Disc, Edit, Trash2 } from 'react-feather';
import MasterTag from 'types/MasterTag';
import { genTableIndex } from 'utils/common';
import { Paging } from 'types/common';
import { useTranslation } from 'react-i18next';

interface IProps {
    items: MasterTag[];
    paging: Paging;
    handleDelete: (id: number) => void;
    handleEdit: (id: number) => void;
}

export default function ListMasterTag({ items, paging, handleDelete, handleEdit }: Readonly<IProps>) {
    const { t } = useTranslation();

    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="text-center">No</th>
                        <th>Tag Name</th>
                        <th className="text-center">Tag Type</th>
                        <th>Display Name</th>
                        <th>Tool tip</th>
                        <th className="thAction1"></th>
                    </tr>
                </thead>
                <tbody>
                    {items.map((item: MasterTag, index: number) => (
                        <tr key={item.id}>
                            <td className="text-center">{genTableIndex(index, paging.limit, paging.current_page)}</td>
                            <td>
                                <span className="text-primary cursor-pointer" onClick={() => handleEdit(item.id!)}>
                                    {item.name}
                                </span>
                            </td>
                            <td className="text-center">{item.type}</td>
                            <td>{item.display_name}</td>
                            <td>{item.tool_tip}</td>
                            <td className="text-center">
                                <button
                                    type="button"
                                    title={t('delete')}
                                    className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                    onClick={() => handleDelete(item.id!)}
                                >
                                    <Trash2 size={14} />
                                </button>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
