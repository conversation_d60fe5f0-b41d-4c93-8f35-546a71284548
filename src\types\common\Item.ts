export interface ItemParam {
    id: number;
    name: string;
    className?: string;
}

export interface ItemParamModel {
    id: string;
    name: string;
    className?: string;
    desc?: string | null;
}

export interface ItemCount extends ItemParam {
    count: number;
}

export interface ItemLink {
    id?: number;
    text: string;
    to?: string;
    icon?: 'PLUS' | 'DOWNLOAD' | 'UPLOAD';
    fnCallBack?: {
        actionMenu: (id: number) => void;
    };
}

export interface ItemId {
    id: number | string;
}

export interface ItemFileUpload {
    id: number;
    name: string;
    url: string;
}

export interface ItemFile {
    id?: number;
    file_name: string;
    file_url: string;
    file_size?: number;
    mime_type?: string;
}

export interface SelectOption {
    value: number;
    label: string;
}

export interface SelectOptionModel {
    value: string;
    label: string;
}

export enum TrueFalse {
    FALSE = 0,
    TRUE = 1,
}

export enum ItemStatus {
    PENDING = 1,
    ACTIVE = 2,
    INACTIVE = 3,
}

export enum ItemParamType {
    GROUP = 'group',
    OTP = 'otp',
}

export const ItemStatusNames: ItemParam[] = [
    { id: ItemStatus.PENDING, name: 'pending', className: 'badge badge-glow bg-warning' },
    { id: ItemStatus.ACTIVE, name: 'active', className: 'badge badge-glow bg-success' },
    { id: ItemStatus.INACTIVE, name: 'locked', className: 'badge badge-glow bg-danger' },
];

export const ItemStatusNames2: ItemParam[] = [
    { id: ItemStatus.ACTIVE, name: 'active', className: 'badge badge-glow bg-success' },
    { id: ItemStatus.INACTIVE, name: 'inactive', className: 'badge badge-glow bg-danger' },
];

export const YesNoChoices: ItemParam[] = [
    { id: TrueFalse.FALSE, name: 'no', className: 'badge badge-glow bg-danger' },
    { id: TrueFalse.TRUE, name: 'yes', className: 'badge badge-glow bg-success' },
];
