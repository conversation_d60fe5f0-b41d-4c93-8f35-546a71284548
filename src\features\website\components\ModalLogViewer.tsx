import { useEffect, useState } from 'react';
import ModalContent from 'components/partials/ModalContent';
import { showToast } from 'utils/common';

interface Props {
    domain: string;
    show: boolean;
    changeShow: (s: boolean) => void;
}

export default function ModalLogViewer({ domain, show, changeShow }: Props) {
    const [logs, setLogs] = useState<{ wordpress: string[]; custom: string[] }>({ wordpress: [], custom: [] });

    useEffect(() => {
        const fetchLogs = async () => {
            try {
                const res = await fetch(`https://${domain}/wp-json/customer-edit/v1/logs?format=json`);
                const data = await res.json();
                if (data?.logs) {
                    setLogs({
                        wordpress: data.logs.wordpress?.split('\n') || [],
                        custom: data.logs.custom?.split('\n') || [],
                    });
                }
            } catch {
                // Handle error gracefully
            }
        };

        if (domain && show) fetchLogs();
    }, [domain, show]);

    const content = (
        <div className="max-h-[400px] overflow-y-auto whitespace-pre-wrap text-sm font-mono space-y-4">
            <div>
                <h4 className="font-bold mb-2 text-blue-600">WordPress Logs</h4>
                {logs.wordpress.map((line, i) => (
                    <div key={i}>{line}</div>
                ))}
            </div>
            <div>
                <h4 className="font-bold mt-4 mb-2 text-green-600">Custom Logs</h4>
                {logs.custom.map((line, i) => (
                    <div key={i}>{line}</div>
                ))}
            </div>
        </div>
    );

    return <ModalContent show={show} changeShow={changeShow} title={`Logs - ${domain}`} content={content} />;
}
