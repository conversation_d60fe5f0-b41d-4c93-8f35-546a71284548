import ContentHeader from 'components/partials/ContentHeader';
import ModalConfirm from 'components/partials/ModalConfirm';
import Spinner from 'components/partials/Spinner';
import { QUERY_KEY, SUCCESS_MESSAGE } from 'constants/common';
import { useGraphQLMutation } from 'hooks/useGraphQLMutation';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import { useCallback, useState } from 'react';
import { Helmet } from 'react-helmet-async';

import { INDUSTRY_CREATE, INDUSTRY_DELETE, INDUSTRY_LIST, INDUSTRY_UPDATE } from 'services/IndustryService';
import { useAuthStore } from 'stores/authStore';
import Industry, { IndustryQuery } from 'types/Industry';
import { showToast } from 'utils/common';
import ListIndustry from '../components/ListIndustry';
import ModalIndustryUpdate from '../components/ModalIndustryUpdate';
import { useTranslation } from 'react-i18next';

export default function IndustryList() {
    const { user } = useAuthStore();
    const { t } = useTranslation();
    const [showDelete, setShowDelete] = useState(false);
    const [showModal, setShowModal] = useState(false);
    const [selectedIndustry, setSelectedIndustry] = useState<Industry | undefined>(undefined);
    const [itemId, setItemId] = useState(0);

    const { data, isLoading, isRefetching, refetch } = useGraphQLQuery<IndustryQuery>(
        [QUERY_KEY.INDUSTRIES],
        INDUSTRY_LIST,
        {
            sort: 'display_order:ASC',
        },
        '',
        {
            enabled: !!user,
        }
    );

    const handleShowMsg = useCallback(
        (isDelete: boolean) => {
            if (isDelete) {
                setShowDelete(false);
                setItemId(0);
            } else {
                setShowModal(false);
            }
            showToast(true, [SUCCESS_MESSAGE]);
            refetch();
        },
        [refetch]
    );

    const deleteMutation = useGraphQLMutation(INDUSTRY_DELETE, '', {
        onSuccess: () => {
            handleShowMsg(true);
        },
    });

    const createMutation = useGraphQLMutation<{}>(INDUSTRY_CREATE, '', {
        onSuccess: () => {
            handleShowMsg(false);
        },
    });

    const updateMutation = useGraphQLMutation<{}>(INDUSTRY_UPDATE, '', {
        onSuccess: () => {
            handleShowMsg(false);
        },
    });

    const handleDelete = (id: number) => {
        setItemId(id);
        setShowDelete(true);
    };

    const deleteItem = () => {
        deleteMutation.mutate({ id: itemId });
    };

    const handleAdd = () => {
        setSelectedIndustry(undefined);
        setShowModal(true);
    };

    const handleEdit = (id: number) => {
        const industry = data?.industry_list.find((item) => item.id === id);
        setSelectedIndustry(industry);
        setShowModal(true);
    };

    const handleSubmit = (formData: Industry) => {
        if (selectedIndustry?.id) {
            updateMutation.mutate({
                id: selectedIndustry.id,
                name: formData.name,
                display_order: formData.display_order,
                status_id: formData.status_id,
            });
        } else {
            createMutation.mutate({
                name: formData.name,
                display_order: formData.display_order,
                status_id: formData.status_id,
            });
        }
    };

    return (
        <>
            <Helmet>
                <title>Industries</title>
            </Helmet>
            <ContentHeader
                title="Industries"
                contextMenu={[
                    {
                        text: 'Add Industry',
                        icon: 'PLUS',
                        fnCallBack: { actionMenu: () => handleAdd() },
                    },
                ]}
            />
            <div className="content-body">
                <div className="col-12">
                    {(isLoading || isRefetching) && <Spinner />}
                    {!isLoading && !isRefetching && (
                        <div className="card">
                            <ListIndustry
                                items={data?.industry_list ?? []}
                                handleDelete={handleDelete}
                                handleEdit={handleEdit}
                            />
                        </div>
                    )}
                    <ModalConfirm
                        show={showDelete}
                        text={t('confirm.delete', { data: 'industry' })}
                        btnDisabled={deleteMutation.isPending}
                        changeShow={(s: boolean) => setShowDelete(s)}
                        submitAction={deleteItem}
                    />
                    <ModalIndustryUpdate
                        show={showModal}
                        industry={selectedIndustry}
                        isLoading={createMutation.isPending || updateMutation.isPending}
                        changeShow={(s: boolean) => setShowModal(s)}
                        submitAction={handleSubmit}
                    />
                </div>
            </div>
        </>
    );
}
