import TabContext from '@mui/lab/TabContext';
import TabList from '@mui/lab/TabList';
import TabPanel from '@mui/lab/TabPanel';
import Box from '@mui/material/Box';
import Tab from '@mui/material/Tab';
import ContentHeader from 'components/partials/ContentHeader';
import Spinner from 'components/partials/Spinner';
import { LIMIT_DEFAULT, PAGE_NUMBER_DEFAULT, PATH, QUERY_KEY, SUCCESS_MESSAGE } from 'constants/common';
import { includes } from 'lodash';
import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { Navigate, useLocation, useNavigate, useParams } from 'react-router-dom';
import { displayUserName, UserChangeGroup, UserDetail, UserRole, UserRoleNames } from 'types/User';
import { getFieldInArrayObject, showToast } from 'utils/common';
import SetPassForm from '../components/SetPassForm';
import UpdateUserForm from '../components/UpdateUserForm';
import UserGroups from '../components/UserGroups';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { USERS_CHANGE_GROUPS, USER_DETAIL } from '../../../services/UserService';
import { GROUP_LIST } from '../../../services/GroupService';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import { GroupQuery } from '../../../types/Group';
import { BaseSearch } from '../../../types/common';

export default function UserEdit() {
    const { id } = useParams();
    const navigate = useNavigate();
    const { hash } = useLocation();
    const { t } = useTranslation();
    const [type, setType] = useState('admin');
    const [tabId, setTabId] = useState('info');
    const [groupIds, setGroupIds] = useState<number[]>([]);

    const { data: userData, isLoading } = useGraphQLQuery<UserDetail, { id: number }>(
        [QUERY_KEY.USER, id],
        USER_DETAIL,
        { id: Number(id) },
        '',
        {
            enabled: !!id,
        }
    );

    const user = userData?.users_detail;
    useEffect(() => {
        if (user) {
            const groupIds = user?.groups ? user.groups.map((group) => Number(group.id)) : [];
            setGroupIds(groupIds);
        }
    }, [user]);

    useEffect(() => {
        if (!isLoading && user) {
            setType(getFieldInArrayObject(UserRoleNames, user.role_id, 'name', 'admin'));
        }
    }, [user, isLoading]);

    useEffect(() => {
        if (hash) {
            let hashId = hash.replace('#', '');
            if (!includes(['info', 'password', 'groups', 'permissions', 'orders', 'crossCheck'], hashId)) {
                hashId = 'info';
            }
            setTabId(hashId);
        }
    }, [hash]);

    const { data: groupData } = useGraphQLQuery<GroupQuery, BaseSearch>(
        [QUERY_KEY.GROUPS],
        GROUP_LIST,
        {
            limit: LIMIT_DEFAULT,
            page: PAGE_NUMBER_DEFAULT,
        },
        '',
        { enabled: user?.role_id === UserRole.ADMIN }
    );
    const groups = groupData?.groups_list.data;

    const updateUserGroupMutation = useGraphQLMutation<UserChangeGroup, { id: number; group_ids: number[] }>(
        USERS_CHANGE_GROUPS,
        '',
        {
            onSuccess() {
                showToast(true, [SUCCESS_MESSAGE]);
                setTimeout(() => {
                    navigate(`/user/list/${type}`);
                }, 2000);
            },
        }
    );

    const handleTabChange = async (_event: React.SyntheticEvent, tab: string) => {
        setTabId(tab);
        navigate('#' + tab);
    };

    const grantGroups = (ids: number[]) => {
        setGroupIds(ids);
        updateUserGroupMutation.mutate({ id: Number(id), group_ids: ids });
    };

    if (!isLoading && !user) return <Navigate to={PATH.NOT_FOUND} />;

    return (
        <>
            <Helmet>
                <title>{t(`${type}.edit`)}</title>
            </Helmet>
            <ContentHeader
                title={t(`${type}.edit`)}
                breadcrumbs={[
                    {
                        text: t(`${type}.multiple`),
                        to: `/user/list/${type}`,
                    },
                    {
                        text: user ? `${displayUserName(user)}` : t(`${type}.edit`),
                        to: '',
                    },
                ]}
            />
            <div className="content-body">
                <div className="col-12">
                    {isLoading && <Spinner />}
                    {!isLoading && user && (
                        <Box sx={{ width: '100%', typography: 'body1' }}>
                            <TabContext value={tabId}>
                                <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                                    <TabList onChange={handleTabChange} className="nav nav-pills mb-2">
                                        <Tab label={t('baseInformation')} value="info" />
                                        <Tab label={t('resetPassword')} value="password" />
                                        {user.role_id === UserRole.ADMIN && (
                                            <Tab label={t('group.multiple')} value="groups" />
                                        )}
                                    </TabList>
                                </Box>
                                <TabPanel value="info" style={{ padding: 0 }}>
                                    <UpdateUserForm id={id ?? ''} roleId={user.role_id} user={user} type={type} />
                                </TabPanel>
                                <TabPanel value="password" style={{ padding: 0 }}>
                                    <SetPassForm id={id ?? ''} type={type} />
                                </TabPanel>
                                {user.role_id === UserRole.ADMIN && (
                                    <TabPanel value="groups" style={{ padding: 0 }}>
                                        <UserGroups
                                            groups={
                                                groups?.map(({ id, name }) => ({ id: id?.toString() ?? '', name })) ??
                                                []
                                            }
                                            groupIds={groupIds ?? []}
                                            loading={false}
                                            handleSubmit={grantGroups}
                                        />
                                    </TabPanel>
                                )}
                            </TabContext>
                        </Box>
                    )}
                </div>
            </div>
        </>
    );
}
