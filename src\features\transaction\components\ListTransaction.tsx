import { genTableIndex, getFieldHtml } from 'utils/common';
import { Paging } from '../../../types/common';
import Transaction, { TransactionStatusNames, TransactionTypeNames } from 'types/Transaction';
import { useTranslation } from 'react-i18next';
import FormatNumber from '../../../components/partials/FormatNumber';
import { FORMAT_DATE, formatDateTime } from '../../../utils/date';
import { Link } from 'react-router-dom';
import UserLink from '../../../components/partials/UserLink';

interface IProps {
    items: Transaction[];
    paging: Paging;
}

export default function ListTransaction({ items, paging }: Readonly<IProps>) {
    const { t } = useTranslation();

    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="text-center">No</th>
                        <th>Customer</th>
                        <th className="text-center">Plan</th>
                        <th className="text-center">Type</th>
                        <th className="text-right">Price</th>
                        <th className="text-center">Start Date</th>
                        <th className="text-center">End Date</th>
                        <th className="text-center">Status</th>
                    </tr>
                </thead>
                <tbody>
                    {items.map((item: Transaction, index: number) => (
                        <tr key={item.id}>
                            <td className="text-center">{genTableIndex(index, paging.limit, paging.current_page)}</td>
                            <td>
                                {item.customer && (
                                    <UserLink
                                        to={`/user/edit/${item.customer.id}`}
                                        avatar={item.customer.avatar?.file_url}
                                        name={item.customer.full_name}
                                    />
                                )}
                            </td>
                            <td className="text-center">
                                {item.plan && (
                                    <Link to={`/subscriptionPlan/edit/${item.plan.id}`}>{item.plan.name}</Link>
                                )}
                            </td>
                            <td className="text-center">{getFieldHtml(TransactionTypeNames, item.type_id, t)}</td>
                            <td className="text-right">
                                <FormatNumber value={item.price} isInput={false} renderText={(value) => value} />
                            </td>
                            <td className="text-center">
                                {formatDateTime(item.start_date, FORMAT_DATE.SHOW_ONLY_DATE)}
                            </td>
                            <td className="text-center">{formatDateTime(item.end_date, FORMAT_DATE.SHOW_ONLY_DATE)}</td>
                            <td className="text-center">{getFieldHtml(TransactionStatusNames, item.status_id, t)}</td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
