import { SelectOption, SelectOptionModel } from 'types/common/Item';

export enum DEFAULT_VALUE {
    IMAGE = '/assets/images/prp/default.png',
}

export const REACT_APP_API_URL = import.meta.env.VITE_REACT_APP_API_URL ?? '';
export const REACT_WP_URL = import.meta.env.VITE_REACT_WP_URL ?? '';
export const W_TOKEN_KEY = import.meta.env.VITE_W_TOKEN_KEY;

export const PAGINATION = {
    countItem: 0,
    totalPage: 1,
    currentPage: 1,
    limit: 10,
};

export const optionConstantDefault: SelectOption = { value: 0, label: '' };
export const optionModelDefault: SelectOptionModel = { value: '', label: '' };

export enum QUERY_KEY {
    ACTIONS = 'actions',
    USERS = 'users',
    USER = 'user',
    PROFILE = 'profile',
    USER_ACTIONS = 'user_actions',
    GROUPS = 'groups',
    LOGOUT = 'logout',
    INDUSTRIES = 'industries',
    WEBSITE_STYLES = 'website_styles',
    WEBSITES = 'websites',
    TEMPLATES = 'templates',
    TEMPLATE = 'template',
    SUBSCRIPTION_PLANS = 'subscription_plans',
    SUBSCRIPTION_PLAN = 'subscription_plan',
    COMPONENTS = 'components',
    COMPONENT = 'component',
    TRANSACTIONS = 'transactions',
    MASTER_TAGS = 'master_tags',
    CONFIGS = 'configs',
    SMTPS = 'smtps',
    TRANSACTION_STATISTICS = 'transaction_statistics',
    TRANSACTIONS_TODAY_STATISTICS = 'transactions_today_statistics',
    BACKUP_WEBSITES_LIST = 'backup_websites_list',
    DNS_RECORDS = 'dns_records',
    CONFIGS_BY_CODE = 'configs_by_code',
    TICKETS = 'tickets',
}

export const SUCCESS_MESSAGE = 'The request has been successful';
export const ERROR_MESSAGE = 'An internal server error occurred';

export const OPERATION_NAME = {
    LOGIN: 'login',
    LOGOUT: 'logout',
    REFRESH_TOKEN: 'refresh_token',
    FORGOT_PASSWORD: 'forgot_password',
    REGISTER: 'register',
} as const;

export const FILTER_CONDITIONS = {
    EQUAL: '=',
    NOT_EQUAL: '!=',
    GREATER_THAN: '>',
    LESS_THAN: '<',
    GREATER_OR_EQUAL: '>=',
    LESS_OR_EQUAL: '<=',
    LIKE: '~',
    NOT_LIKE: '!~',
    IN: '[]',
    NOT_INT: '![]',
} as const;

export const PATH = {
    HOME: '/',
    NOT_FOUND: 'not-found',
} as const;

export const PAGE_NUMBER_DEFAULT = 1 as const;
export const LIMIT_DEFAULT = 100 as const;

export const BAR_COLORS = ['#10b981', '#ef4444'];
export const FONT_STYLE = { fontSize: '14px' };

export const DNS_RECORD_TYPES = [
    { type: 'A', description: 'Maps a domain to an IPv4 address' },
    { type: 'AAAA', description: 'Maps a domain to an IPv6 address' },
    { type: 'CNAME', description: 'Canonical name (alias)' },
    { type: 'MX', description: 'Mail exchange record' },
    { type: 'TXT', description: 'Text record (used for SPF, verification)' },
    { type: 'SRV', description: 'Service locator (used for VOIP, etc.)' },
    { type: 'LOC', description: 'Location information' },
    { type: 'SPF', description: 'Deprecated — still supported for legacy' },
    { type: 'NS', description: 'Name server record (used for delegation)' },
    { type: 'PTR', description: 'Pointer record (reverse DNS)' },
    { type: 'CERT', description: 'Certificate record' },
    { type: 'DNSKEY', description: 'DNSSEC public key' },
    { type: 'DS', description: 'Delegation signer (DNSSEC)' },
    { type: 'NAPTR', description: 'Naming authority pointer' },
    { type: 'SMIMEA', description: 'Email certificate mapping (DNSSEC)' },
    { type: 'SSHFP', description: 'SSH key fingerprint' },
    { type: 'SVCB', description: 'Service binding (modern replacement for SRV)' },
    { type: 'TLSA', description: 'TLS Authentication record' },
    { type: 'URI', description: 'Uniform Resource Identifier record' },
    { type: 'HTTPS', description: 'HTTPS-specific record (for SVCB)' },
];

export const TTL_OPTIONS = [
    { value: 1, label: 'Auto' },
    { value: 120, label: '2 minutes' },
    { value: 300, label: '5 minutes' },
    { value: 600, label: '10 minutes' },
    { value: 1800, label: '30 minutes' },
    { value: 3600, label: '1 hour' },
    { value: 7200, label: '2 hours' },
    { value: 18000, label: '5 hours' },
    { value: 43200, label: '12 hours' },
    { value: 86400, label: '24 hours' },
];

export const PLUGIN_OPTIONS = [
    { label: 'Wordfence', command: 'plugin install wordfence --activate' },
    { label: 'Miniorange', command: 'plugin install miniorange-saml-20-single-sign-on --activate' },
    { label: 'Instant Images', command: 'plugin install instant-images --activate' },
];
