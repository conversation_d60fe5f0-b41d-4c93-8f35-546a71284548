import { Edit } from 'react-feather';
import Ticket, { TicketStatus, TicketStatusNames, TicketTypeNames } from 'types/Ticket';
import { ItemStatusNames } from 'types/common/Item';
import { getFieldHtml } from '../../../utils/common';
import { useTranslation } from 'react-i18next';

interface IProps {
    items: Ticket[];
    handleEdit: (id: number) => void;
}

export default function ListTicket({ items, handleEdit }: Readonly<IProps>) {
    const { t } = useTranslation();

    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="text-center">No</th>
                        <th className="text-center">Title</th>
                        <th className="text-center">Customer</th>
                        <th className="text-center">Type</th>
                        <th className="text-center">Status</th>
                        <th className="thAction1"></th>
                    </tr>
                </thead>
                <tbody>
                    {items.map((item: Ticket, index) => (
                        <tr key={item.id}>
                            <td className="text-center">{index + 1}</td>
                            <td className="text-center">{item.title}</td>
                            <td className="text-center">{item.customer.full_name}</td>
                            <td className="text-center">{getFieldHtml(TicketTypeNames, item.type_id, t)}</td>
                            <td className="text-center">{getFieldHtml(TicketStatusNames, item.status_id, t)}</td>
                            <td className="text-center">
                                <button
                                    className="btn btn-icon btn-sm btn-flat-primary waves-effect"
                                    onClick={() => handleEdit(item.id!)}
                                >
                                    <Edit size={14} />
                                </button>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
