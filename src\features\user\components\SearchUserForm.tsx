import SearchForm from 'components/partials/SearchForm';
import { filter } from 'lodash';
import { useTranslation } from 'react-i18next';
import { ItemStatusNames } from 'types/common/Item';
import { SearchField } from 'types/common/Search';
import { convertConstantToSelectOptions, convertSelectToStringKey } from 'utils/common';

interface IProps {
    isLoading: boolean;
}

export default function SearchUserForm({ isLoading }: Readonly<IProps>) {
    const { t } = useTranslation();

    const fields: SearchField[] = [
        { name: 'search', type: 'text', label: t('keywords'), wrapClassName: 'col-md-4 col-12', show: true },
        {
            name: 'status_id',
            type: 'select',
            label: t('statusName'),
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: true,
                choices: convertSelectToStringKey(convertConstantToSelectOptions(ItemStatusNames, t, true)),
            },
            show: true,
        },
    ];

    return <SearchForm fields={filter(fields, { show: true })} isLoading={isLoading} />;
}
