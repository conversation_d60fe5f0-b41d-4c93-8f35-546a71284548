import { FILTER_CONDITIONS } from 'constants/common';
import User from './User';
import { baseFilterConfig, BaseModel, BaseSearch, DataList, FilterConfig } from './common';
import { ItemParam } from './common/Item';

export enum TicketType {
    WEBSITE_DOWN = 1,
    DOMAIN = 2,
    OTHER = 3,
}

export enum TicketStatus {
    PENDING = 1,
    PROCESSING = 2,
    FINISH = 3,
}

export default interface Ticket extends BaseModel {
    title: string;
    feedback: string;
    customer: User;
    content: string;
    type_id: TicketType;
    status_id: TicketStatus;
}

export interface TicketQuery {
    tickets_list: DataList<Ticket>;
}

export interface SearchTicket extends BaseSearch {
    type_id?: string;
}

export type SearchTicketParam = {
    [key in keyof SearchTicket]: string;
};

export const ticketFilterConfig: FilterConfig = {
    ...baseFilterConfig,
    type_id: { key: 'type_id', operator: FILTER_CONDITIONS.IN },
};

export const TicketTypeNames: ItemParam[] = [
    { id: TicketType.WEBSITE_DOWN, name: 'websiteDown', className: 'badge badge-glow bg-danger' },
    { id: TicketType.DOMAIN, name: 'domain', className: 'badge badge-glow bg-warning' },
    { id: TicketType.OTHER, name: 'other', className: 'badge badge-glow bg-warning' },
];

export const TicketStatusNames: ItemParam[] = [
    { id: TicketStatus.PENDING, name: 'pending', className: 'badge badge-glow bg-danger' },
    { id: TicketStatus.PROCESSING, name: 'processing', className: 'badge badge-glow bg-warning' },
    { id: TicketStatus.FINISH, name: 'finish', className: 'badge badge-glow bg-success' },
];
