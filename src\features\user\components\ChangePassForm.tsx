import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import UpdateButton from 'components/partials/UpdateButton';
import { useState } from 'react';
import { Eye, EyeOff } from 'react-feather';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { AuthUpdateRes, ChangePassword } from 'types/User';
import { ItemId } from 'types/common/Item';
import { showToast } from 'utils/common';
import * as yup from 'yup';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import { AUTH_CHANGE_PASSWORD } from '../../../services/UserService';
import { SUCCESS_MESSAGE } from '../../../constants/common';
import { useNavigate } from 'react-router-dom';

export default function ChangePassForm({ id }: Readonly<ItemId>) {
    const navigate = useNavigate();
    const [showOldPass, setShowOldPass] = useState(false);
    const [showPass, setShowPass] = useState(false);
    const [showRePass, setShowRePass] = useState(false);
    const { t } = useTranslation();
    const schema = yup
        .object({
            old_password: yup.string().required('Please enter old password.').trim().min(6, t('error.passwordLength')),
            password: yup
                .string()
                .required('Please enter new password.')
                .trim()
                .min(12, 'Use 12 characters or more for your password.')
                .matches(/[A-Z]/, 'The password should contain at least one uppercase letter.')
                .matches(/[a-z]/, 'The password should contain at least one lowercase letter.')
                .matches(/[0-9]/, 'The password should contain at least one numeric character.')
                .matches(/[^A-Za-z0-9]/, 'The password should contain at least one special character.'),
            confirm_password: yup
                .string()
                .required('Please enter confirm password.')
                .trim()
                .min(12, 'Use 12 characters or more for your password.')
                .matches(/[A-Z]/, 'The password should contain at least one uppercase letter.')
                .matches(/[a-z]/, 'The password should contain at least one lowercase letter.')
                .matches(/[0-9]/, 'The password should contain at least one numeric character.')
                .matches(/[^A-Za-z0-9]/, 'The password should contain at least one special character.')
                .oneOf([yup.ref('password'), null], t('error.passwordNotSame')),
        })
        .required();
    const {
        register,
        handleSubmit,
        formState: { isSubmitting, errors },
    } = useForm<ChangePassword>({
        resolver: yupResolver(schema),
    });

    const authChangePasswordMutation = useGraphQLMutation<AuthUpdateRes, ChangePassword>(AUTH_CHANGE_PASSWORD, '', {
        onSuccess() {
            showToast(true, [SUCCESS_MESSAGE]);
            setTimeout(() => {
                navigate(`/user/profile`);
            }, 2000);
        },
    });

    const onSubmit = async (data: ChangePassword) => {
        if (id) {
            authChangePasswordMutation.mutate({
                old_password: data.old_password,
                password: data.password,
                confirm_password: data.confirm_password,
            });
        }
    };

    return (
        <div className="card">
            <div className="card-header border-bottom">
                <h4 className="card-title">{t('changePassword')}</h4>
            </div>
            <div className="card-body pt-1">
                <form className="validate-form" onSubmit={handleSubmit(onSubmit)}>
                    <div className="row">
                        <div className="col-12 col-sm-6 mb-1">
                            <label className="form-label">{t('oldPassword')}</label>
                            <div className="input-group input-group-merge form-password-toggle">
                                <input
                                    {...register('old_password')}
                                    className={classNames('form-control', 'form-control-merge', {
                                        error: Boolean(errors.old_password?.message),
                                    })}
                                    type={showOldPass ? 'text' : 'password'}
                                    placeholder="············"
                                />
                                <span
                                    className="input-group-text cursor-pointer"
                                    onClick={() => setShowOldPass((prevShowOldPass) => !prevShowOldPass)}
                                    style={{
                                        borderColor: errors.old_password?.message ? '#ea5455' : '#d8d6de',
                                    }}
                                >
                                    {showOldPass ? <EyeOff size={14} /> : <Eye size={14} />}
                                </span>
                            </div>
                            <span className="error">{errors.old_password?.message}</span>
                        </div>
                        <div className="col-12 col-sm-6 mb-1"></div>
                        <div className="col-12 col-sm-6 mb-1">
                            <label className="form-label">{t('newPassword')}</label>
                            <div className="input-group input-group-merge form-password-toggle">
                                <input
                                    {...register('password')}
                                    className={classNames('form-control', 'form-control-merge', {
                                        error: Boolean(errors.password?.message),
                                    })}
                                    type={showPass ? 'text' : 'password'}
                                    placeholder="············"
                                />
                                <span
                                    className="input-group-text cursor-pointer"
                                    onClick={() => setShowPass((prevShowPass) => !prevShowPass)}
                                    style={{
                                        borderColor: errors.password?.message ? '#ea5455' : '#d8d6de',
                                    }}
                                >
                                    {showPass ? <EyeOff size={14} /> : <Eye size={14} />}
                                </span>
                            </div>
                            <span className="error">{errors.password?.message}</span>
                        </div>
                        <div className="col-12 col-sm-6 mb-1">
                            <label className="form-label">{t('rePassword')}</label>
                            <div className="input-group input-group-merge form-password-toggle">
                                <input
                                    {...register('confirm_password')}
                                    className={classNames('form-control', 'form-control-merge', {
                                        error: Boolean(errors.confirm_password?.message),
                                    })}
                                    type={showRePass ? 'text' : 'password'}
                                    placeholder="············"
                                />
                                <span
                                    className="input-group-text cursor-pointer"
                                    onClick={() => setShowRePass((prevShowRePass) => !prevShowRePass)}
                                    style={{
                                        borderColor: errors.confirm_password?.message ? '#ea5455' : '#d8d6de',
                                    }}
                                >
                                    {showRePass ? <EyeOff size={14} /> : <Eye size={14} />}
                                </span>
                            </div>
                            <span className="error">{errors.confirm_password?.message}</span>
                        </div>
                        <div className="col-12">
                            <UpdateButton btnText={t('changePassword')} isLoading={isSubmitting} btnClass={['mt-1']} />
                        </div>
                    </div>
                </form>
            </div>
        </div>
    );
}
