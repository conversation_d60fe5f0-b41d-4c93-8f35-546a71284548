import { Trash2 } from 'react-feather';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { ItemPlanTypeNames, SubscriptionPlan } from 'types/SubcriptionPlan';
import { ItemStatus, ItemStatusNames } from 'types/common/Item';
import { getFieldHtml } from 'utils/common';
import FormatNumber from '../../../components/partials/FormatNumber';

interface IProps {
    items: SubscriptionPlan[];
    handleDelete: (id: number) => void;
}

export default function ListSubPlan({ items, handleDelete }: Readonly<IProps>) {
    const { t } = useTranslation();

    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="text-center">No</th>
                        <th>Name</th>
                        <th>Description</th>
                        <th>Price</th>
                        <th className="text-center">Plan Type</th>
                        <th className="text-center">Status</th>
                        <th className="thAction1"></th>
                    </tr>
                </thead>
                <tbody>
                    {items.map((item: SubscriptionPlan, index: number) => (
                        <tr key={item.id}>
                            <td className="text-center">{index + 1}</td>
                            <td>
                                <Link to={`/subscriptionPlan/edit/${item.id}`}>
                                    <span>{item.name}</span>
                                </Link>
                            </td>
                            <td>{item.desc}</td>
                            <td>
                                <FormatNumber value={item.price} isInput={false} renderText={(value) => value} />
                            </td>
                            <td className="text-center">{getFieldHtml(ItemPlanTypeNames, item.type_id, t)}</td>
                            <td className="text-center">{getFieldHtml(ItemStatusNames, item.status_id, t)}</td>
                            <td className="text-center">
                                {item.status_id !== ItemStatus.ACTIVE && (
                                    <button
                                        type="button"
                                        title={t('delete')}
                                        className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                        onClick={() => handleDelete(item.id!)}
                                    >
                                        <Trash2 size={14} />
                                    </button>
                                )}
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
