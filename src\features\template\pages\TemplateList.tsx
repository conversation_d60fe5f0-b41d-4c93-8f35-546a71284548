import { keepPreviousData } from '@tanstack/react-query';
import ContentHeader from 'components/partials/ContentHeader';
import ModalConfirm from 'components/partials/ModalConfirm';
import Spinner from 'components/partials/Spinner';
import { PAGINATION, QUERY_KEY, SUCCESS_MESSAGE } from 'constants/common';
import useQueryParams from 'hooks/useQueryParams';
import isUndefined from 'lodash/isUndefined';
import omitBy from 'lodash/omitBy';
import { useEffect, useMemo, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import Template, {
    SearchTemplateParam,
    templateFilterConfig,
    TemplateQuery,
    TemplateType,
    TemplateTypeNames,
} from 'types/Template';
import { convertPaging, generateFilters, getFieldInArrayObject, showToast } from 'utils/common';
import SearchTemplateForm from '../components/SearchTemplateForm';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import { TEMPLATE_DELETE, TEMPLATE_LIST } from 'services/TemplateService';
import ListTemplate from '../components/ListTemplate';
import PaginationTable from '../../../components/partials/PaginationTable';
import { useNavigate, useParams } from 'react-router-dom';

export default function TemplateList() {
    const { t } = useTranslation();
    const { type } = useParams();
    const navigate = useNavigate();
    const typeId = useMemo(() => +getFieldInArrayObject(TemplateTypeNames, type ?? '', 'id', '-1', 'name'), [type]);
    if (typeId === -1) {
        navigate('/not-found');
    }

    const { queryParams, setQueryParams } = useQueryParams<SearchTemplateParam>();
    const paramConfig: SearchTemplateParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            search: queryParams.search,
            status_id: queryParams.status_id,
            designer_id: queryParams.designer_id,
            industry_id: queryParams.industry_id,
            is_kit: typeId.toString(),
        },
        isUndefined
    );
    const { search, limit, page, ...dataParamConfig } = paramConfig;
    const filters = generateFilters(dataParamConfig, templateFilterConfig);

    const [showDelete, setShowDelete] = useState(false);
    const [itemId, setItemId] = useState(0);

    const { data, isLoading, isRefetching, refetch } = useGraphQLQuery<TemplateQuery>(
        [QUERY_KEY.TEMPLATES, paramConfig, filters],
        TEMPLATE_LIST,
        {
            page: Number(page),
            limit: limit,
            search,
            filters: filters.length > 0 ? filters : undefined,
        },
        '',
        {
            enabled: typeId > -1,
            placeholderData: keepPreviousData,
        }
    );

    const deleteMutation = useGraphQLMutation(TEMPLATE_DELETE, '', {
        onSuccess: () => {
            showToast(true, [SUCCESS_MESSAGE]);
            setShowDelete(false);
            setItemId(0);
            refetch();
        },
    });

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    const handleDelete = (id: number) => {
        deleteMutation.reset();
        setItemId(id);
        setShowDelete(true);
    };

    const deleteItem = async () => {
        if (itemId) {
            deleteMutation.mutate({ id: itemId });
        }
    };

    return (
        <>
            <Helmet>
                <title>{t(`${type}.multiple`)}</title>
            </Helmet>
            <ContentHeader
                title={t(`${type}.multiple`)}
                contextMenu={
                    type === 'kit'
                        ? [
                              {
                                  text: 'Add Template',
                                  to: `/template/add/${type}`,
                                  icon: 'PLUS',
                              },
                          ]
                        : []
                }
            />
            <div className="content-body">
                <div className="col-12">
                    <SearchTemplateForm isLoading={isLoading || isRefetching} />
                    {(isLoading || isRefetching) && <Spinner />}
                    {!isLoading && !isRefetching && (
                        <div className="card">
                            <ListTemplate
                                items={data?.template_list.data ?? []}
                                typeId={typeId}
                                paging={convertPaging<Template, SearchTemplateParam>(data?.template_list, paramConfig)}
                                handleDelete={handleDelete}
                            />
                            <PaginationTable
                                countItem={data?.template_list.totalCount}
                                totalPage={data?.template_list.totalPages}
                                currentPage={data?.template_list.currentPage}
                                handlePageChange={handlePageChange}
                            />
                        </div>
                    )}
                    <ModalConfirm
                        show={showDelete}
                        text={t('confirm.delete', { data: 'template' })}
                        btnDisabled={deleteMutation.isPending}
                        changeShow={(s: boolean) => setShowDelete(s)}
                        submitAction={deleteItem}
                    />
                </div>
            </div>
        </>
    );
}
