import { Setting<PERSON>, Trash2 } from 'react-feather';
import { useTranslation } from 'react-i18next';
import UserLink from 'components/partials/UserLink';
import User, { UserRole, displayUserName } from 'types/User';
import { Paging } from 'types/common';
import { ItemStatus, ItemStatusNames } from 'types/common/Item';
import { genTableIndex, getFieldHtml } from 'utils/common';

interface IProps {
    items: User[];
    paging: Paging;
    handleDelete: (id: number) => void;
    currenUser: User | null;
    roleId: UserRole;
    handleSmtp: (id: number) => void;
}

export default function ListUser({ items, paging, handleDelete, currenUser, roleId, handleSmtp }: Readonly<IProps>) {
    const { t } = useTranslation();

    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="text-center">No</th>
                        <th>Full Name</th>
                        {roleId === UserRole.ADMIN && <th>Phone Number</th>}
                        <th>Email</th>
                        <th className="text-center">Status</th>
                        {roleId === UserRole.CUSTOMER && <th>Industry</th>}
                        {roleId === UserRole.CUSTOMER && <th className="text-center">SMTP</th>}
                        <th className="thAction1"></th>
                    </tr>
                </thead>
                <tbody>
                    {items.map((item: User, index: number) => (
                        <tr key={item.id}>
                            <td className="text-center">{genTableIndex(index, paging.limit, paging.current_page)}</td>
                            <td>
                                <UserLink
                                    to={`/user/edit/${item.id}`}
                                    avatar={item.avatar?.file_url}
                                    name={displayUserName(item)}
                                />
                            </td>
                            {roleId === UserRole.ADMIN && <td>{item.phone_number}</td>}
                            <td>{item.email}</td>
                            <td className="text-center">{getFieldHtml(ItemStatusNames, item.status_id, t)}</td>
                            {roleId === UserRole.CUSTOMER && <td>{item.industry?.name}</td>}
                            {roleId === UserRole.CUSTOMER && (
                                <td className="text-center">
                                    <button
                                        type="button"
                                        className={`btn btn-icon btn-sm waves-effect ${
                                            item.smtp_config ? 'btn-flat-success' : 'btn-flat-primary'
                                        }`}
                                        title={item.smtp_config ? 'Edit SMTP Config' : 'Setup SMTP Config'}
                                        onClick={() => handleSmtp(item.id!)}
                                    >
                                        <Settings size={14} />
                                    </button>
                                </td>
                            )}
                            <td className="text-center">
                                {item.status_id !== ItemStatus.ACTIVE && currenUser?.id !== item.id && (
                                    <button
                                        type="button"
                                        title={t('delete')}
                                        className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                        onClick={() => handleDelete(item.id!)}
                                    >
                                        <Trash2 size={14} />
                                    </button>
                                )}
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
