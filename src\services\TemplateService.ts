import { gql } from 'graphql-request';

export const TEMPLATE_LIST = gql`
    query Template_list($page: Int!, $limit: Int!, $filters: [String!], $sort: String, $search: String) {
        template_list(body: { page: $page, limit: $limit, filters: $filters, sort: $sort, search: $search }) {
            totalCount
            totalPages
            currentPage
            data {
                id
                name
                desc
                status_id
                designer_id
                image_id
                is_multiple
                is_kit
                designer {
                    id
                    full_name
                    avatar {
                        file_url
                    }
                }
                image {
                    file_url
                }
                industries {
                    id
                    name
                    display_order
                    status_id
                }
            }
        }
    }
`;

export const TEMPLATE_CREATE = gql`
    mutation Template_create($body: TemplateSaveInputDto!) {
        template_create(body: $body) {
            id
        }
    }
`;

export const TEMPLATE_UPDATE = gql`
    mutation Template_update($id: Int!, $body: TemplateSaveInputDto!) {
        template_update(id: $id, body: $body) {
            id
        }
    }
`;

export const TEMPLATE_DELETE = gql`
    mutation Template_delete($id: Int!) {
        template_delete(id: $id)
    }
`;

export const TEMPLATE_VIEW = gql`
    query Template_view($id: Int!) {
        template_view(id: $id) {
            id
            name
            desc
            status_id
            designer_id
            image_id
            csv_file_id
            code_file_id
            template_file_id
            sql_file_id
            is_multiple
            info
            approved_date
            is_kit
            designer {
                id
                full_name
                avatar {
                    file_url
                }
            }
            image {
                file_url
            }
            industries {
                id
            }
            csvFile {
                id
                file_name
                file_url
            }
            codeFile {
                id
                file_name
                file_url
            }
            templateFile {
                id
                file_name
                file_url
            }
            sqlFile {
                id
                file_name
                file_url
            }
        }
    }
`;
