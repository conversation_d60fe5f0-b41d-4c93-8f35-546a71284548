import { NotFound, PrivateRoute } from 'components/commons';
import WebsiteList from 'features/website/pages/WebsiteList';
import React from 'react';
import { BrowserRouter, Route, Routes } from 'react-router-dom';

const AuthPage = React.lazy(() => import('features/auth/pages/AuthPage'));
const LoginPage = React.lazy(() => import('features/auth/pages/Login'));
const ForgotPage = React.lazy(() => import('features/auth/pages/ForgotPass'));
const ChangePassPage = React.lazy(() => import('features/auth/pages/ChangePass'));

const Dashboard = React.lazy(() => import('features/dashboard/pages/Dashboard'));
const UserList = React.lazy(() => import('features/user/pages/UserList'));
const UserAdd = React.lazy(() => import('features/user/pages/UserAdd'));
const UserEdit = React.lazy(() => import('features/user/pages/UserEdit'));
const UserProfile = React.lazy(() => import('features/user/pages/UserProfile'));
const ActionList = React.lazy(() => import('features/setting/pages/ActionList'));
const GroupList = React.lazy(() => import('features/setting/pages/GroupList'));
const TemplateList = React.lazy(() => import('features/template/pages/TemplateList'));
const TemplateAdd = React.lazy(() => import('features/template/pages/TemplateAdd'));
const TemplateEdit = React.lazy(() => import('features/template/pages/TemplateEdit'));
const ComponentList = React.lazy(() => import('features/component/pages/ComponentList'));
const ComponentAdd = React.lazy(() => import('features/component/pages/ComponentAdd'));
const ComponentEdit = React.lazy(() => import('features/component/pages/ComponentEdit'));
const SubscriptionPlanList = React.lazy(() => import('features/subscriptionPlan/pages/SubscriptionPlanList'));
//const SubscriptionPlanAdd = React.lazy(() => import('features/subscriptionPlan/pages/SubscriptionPlanAdd'));
const SubscriptionPlanEdit = React.lazy(() => import('features/subscriptionPlan/pages/SubscriptionPlanEdit'));
const IndustryList = React.lazy(() => import('features/industry/pages/IndustryList'));
const WebsiteStyleList = React.lazy(() => import('features/websiteStyle/pages/WebsiteStyleList'));
const TransactionList = React.lazy(() => import('features/transaction/pages/TransactionList'));
const MasterTagList = React.lazy(() => import('features/masterTag/pages/MasterTagList'));
const ConfigList = React.lazy(() => import('features/config/pages/ConfigList'));
const TicketList = React.lazy(() => import('features/ticket/pages/TicketList'));

export default function RouterView() {
    return (
        <BrowserRouter
            future={{
                v7_startTransition: true,
                v7_relativeSplatPath: true,
            }}
        >
            <Routes>
                <Route path="/" element={<AuthPage />}>
                    <Route index element={<LoginPage />} />
                    <Route path="forgot" element={<ForgotPage />} />
                    <Route path="change-password" element={<ChangePassPage />} />
                </Route>
                <Route
                    path="/dashboard"
                    element={
                        <PrivateRoute>
                            <Dashboard />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/user/list/:type"
                    element={
                        <PrivateRoute>
                            <UserList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/user/add/:type"
                    element={
                        <PrivateRoute>
                            <UserAdd />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/user/edit/:id"
                    element={
                        <PrivateRoute>
                            <UserEdit />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/user/profile"
                    element={
                        <PrivateRoute>
                            <UserProfile />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/action"
                    element={
                        <PrivateRoute>
                            <ActionList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/group"
                    element={
                        <PrivateRoute>
                            <GroupList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/template/list/:type"
                    element={
                        <PrivateRoute>
                            <TemplateList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/template/add/:type"
                    element={
                        <PrivateRoute>
                            <TemplateAdd />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/template/edit/:id"
                    element={
                        <PrivateRoute>
                            <TemplateEdit />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/component"
                    element={
                        <PrivateRoute>
                            <ComponentList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/component/add"
                    element={
                        <PrivateRoute>
                            <ComponentAdd />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/component/edit/:id"
                    element={
                        <PrivateRoute>
                            <ComponentEdit />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/subscriptionPlan"
                    element={
                        <PrivateRoute>
                            <SubscriptionPlanList />
                        </PrivateRoute>
                    }
                ></Route>
                {/*<Route*/}
                {/*    path="/subscriptionPlan/add/"*/}
                {/*    element={*/}
                {/*        <PrivateRoute>*/}
                {/*            <SubscriptionPlanAdd />*/}
                {/*        </PrivateRoute>*/}
                {/*    }*/}
                {/*></Route>*/}
                <Route
                    path="/subscriptionPlan/edit/:id"
                    element={
                        <PrivateRoute>
                            <SubscriptionPlanEdit />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/industry"
                    element={
                        <PrivateRoute>
                            <IndustryList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/websiteStyle"
                    element={
                        <PrivateRoute>
                            <WebsiteStyleList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/website"
                    element={
                        <PrivateRoute>
                            <WebsiteList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/transaction"
                    element={
                        <PrivateRoute>
                            <TransactionList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/masterTag"
                    element={
                        <PrivateRoute>
                            <MasterTagList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/config"
                    element={
                        <PrivateRoute>
                            <ConfigList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/ticket"
                    element={
                        <PrivateRoute>
                            <TicketList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route path="/not-found" element={<NotFound />}></Route>
                <Route path="*" element={<NotFound />}></Route>
            </Routes>
        </BrowserRouter>
    );
}
