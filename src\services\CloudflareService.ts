import { gql } from 'graphql-request';

export const CLOUDFLARE_GET_DNS_RECORDS = gql`
    query Cloudflare_get_dns_records($domain: String!) {
        cloudflare_get_dns_records(domain: $domain) {
            id
            type
            name
            content
            ttl
            priority
            proxied
            zone_id
            zone_name
            created_on
            modified_on
        }
    }
`;

export const CLOUDFLARE_CREATE_DNS_RECORD = gql`
    mutation Cloudflare_create_dns_record($input: CreateDnsRecordInput!) {
        cloudflare_create_dns_record(input: $input) {
            id
            type
            name
            content
            ttl
            priority
            proxied
            zone_id
            zone_name
            created_on
            modified_on
        }
    }
`;

export const CLOUDFLARE_UPDATE_DNS_RECORD = gql`
    mutation Cloudflare_update_dns_record($input: CloudflareDNSRecordInput!) {
        cloudflare_update_dns_record(input: $input) {
            id
            type
            name
            content
            ttl
            priority
            proxied
            zone_id
            zone_name
            created_on
            modified_on
        }
    }
`;

export const CLOUDFLARE_DELETE_DNS_RECORD = gql`
    mutation Cloudflare_delete_dns_record($domain: String!, $recordId: String!) {
        cloudflare_delete_dns_record(domain: $domain, recordId: $recordId)
    }
`;

export const CLOUDFLARE_DELETE_ZONE_BY_NAME = gql`
    mutation Cloudflare_delete_zone_by_name($domain: String!) {
        cloudflare_delete_zone_by_name(domain: $domain) {
            success
            message
        }
    }
`;
