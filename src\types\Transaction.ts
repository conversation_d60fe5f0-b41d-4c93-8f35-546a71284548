import { baseFilterConfig, BaseModel, BaseSearch, DataList, FilterConfig } from './common';
import { FILTER_CONDITIONS } from '../constants/common';
import { ItemParam, ItemStatus } from './common/Item';

export enum TransactionType {
    WEBSITE = 1,
    DOMAIN = 2,
}

export enum TransactionStatus {
    PAID = 1,
    UNPAID = 2,
}

export default interface Transaction extends BaseModel {
    customer_id: number;
    plan_id?: number;
    type_id: TransactionType;
    price: number;
    status_id: ItemStatus;
    start_date: string;
    end_date: string;
    plan?: {
        id: number;
        name: string;
    };
    customer?: {
        id: number;
        full_name: string;
        avatar: {
            file_url: string;
        };
    };
}

export interface TransactionQuery {
    transactions_list: DataList<Transaction>;
}

export interface SearchTransaction extends BaseSearch {
    type_id?: string;
    plan_id?: string;
    customer_id?: string;
}

export type SearchTransactionParam = {
    [key in keyof SearchTransaction]: string;
};

export const transactionFilterConfig: FilterConfig = {
    ...baseFilterConfig,
    customer_id: { key: 'customer_id', operator: FILTER_CONDITIONS.IN },
    plan_id: { key: 'plan_id', operator: FILTER_CONDITIONS.IN },
    type_id: { key: 'type_id', operator: FILTER_CONDITIONS.IN },
};

export const TransactionTypeNames: ItemParam[] = [
    { id: TransactionType.WEBSITE, name: 'website', className: 'badge badge-glow bg-primary' },
    { id: TransactionType.DOMAIN, name: 'domain', className: 'badge badge-glow bg-success' },
];

export const TransactionStatusNames: ItemParam[] = [
    { id: TransactionStatus.PAID, name: 'paid', className: 'badge badge-glow bg-success' },
    { id: TransactionStatus.UNPAID, name: 'unpaid', className: 'badge badge-glow bg-danger' },
];
