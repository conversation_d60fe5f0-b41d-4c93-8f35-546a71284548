import SearchForm from 'components/partials/SearchForm';
import { filter } from 'lodash';
import { useTranslation } from 'react-i18next';
import { ItemStatusNames } from 'types/common/Item';
import { SearchField } from 'types/common/Search';
import { convertConstantToSelectOptions, convertSelectToStringKey, generateFilters } from 'utils/common';
import { ItemPlanTypeNames } from '../../../types/SubcriptionPlan';

interface IProps {
    isLoading: boolean;
}

export default function SearchSubPlanForm({ isLoading }: Readonly<IProps>) {
    const { t } = useTranslation();

    const fields: SearchField[] = [
        { name: 'search', type: 'text', label: t('keywords'), wrapClassName: 'col-md-4 col-12' },
        {
            name: 'status_id',
            type: 'select',
            label: t('statusName'),
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: true,
                choices: convertSelectToStringKey(convertConstantToSelectOptions(ItemStatusNames, t, true)),
            },
        },
        {
            name: 'type_id',
            type: 'select',
            label: t('subscription_plan.plan_type'),
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: true,
                choices: convertSelectToStringKey(convertConstantToSelectOptions(ItemPlanTypeNames, t, true)),
            },
        },
    ];

    return <SearchForm fields={fields} isLoading={isLoading} />;
}
