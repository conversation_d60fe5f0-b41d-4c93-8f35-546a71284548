import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import UpdateButton from 'components/partials/UpdateButton';
import { useEffect, useLayoutEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import Select from 'react-select';
import WebsiteStyle from 'types/WebsiteStyle';
import { ItemStatus, SelectOption } from 'types/common/Item';
import { getSelectStyle, toggleModalOpen } from 'utils/common';
import * as yup from 'yup';
import { useTranslation } from 'react-i18next';

interface IProps {
    show: boolean;
    websiteStyle?: WebsiteStyle;
    isLoading?: boolean;
    changeShow: (s: boolean) => void;
    submitAction: (data: WebsiteStyle) => void;
}

export default function ModalWebsiteStylesUpdate({
    show,
    websiteStyle,
    isLoading,
    changeShow,
    submitAction,
}: Readonly<IProps>) {
    const { t } = useTranslation();
    useLayoutEffect(() => toggleModalOpen(show), [show]);

    const schema = yup
        .object({
            name: yup.string().required(t('error.required')).trim(),
            desc: yup.string().required(t('error.required')).trim(),
            display_order: yup
                .number()
                .typeError(t('error.number'))
                .required(t('error.required'))
                .min(1, t('error.min_1'))
                .max(99, t('error.max_99')),
            status_id: yup.number().min(1, t('error.required')),
        })
        .required();

    const {
        register,
        handleSubmit,
        control,
        reset,
        formState: { errors },
    } = useForm<WebsiteStyle>({
        resolver: yupResolver(schema),
    });

    useEffect(() => {
        if (websiteStyle && show) {
            reset(websiteStyle);
        } else {
            reset({
                name: '',
                desc: '',
                display_order: 1,
                status_id: ItemStatus.ACTIVE,
            });
        }
    }, [websiteStyle, show, reset]);

    const statusOptions: SelectOption[] = [
        { value: ItemStatus.ACTIVE, label: 'Active' },
        { value: ItemStatus.INACTIVE, label: 'Inactive' },
    ];

    return (
        <>
            <div
                className={classNames('modal fade text-start modal-primary', { show })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-dialog-centered">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">{websiteStyle ? 'Edit Website Style' : 'Add Website Style'}</h5>
                            <button type="button" className="btn-close" onClick={() => changeShow(false)} />
                        </div>
                        <form className="validate-form pt-50" onSubmit={handleSubmit(submitAction)}>
                            <div className="modal-body">
                                <div className="row">
                                    <div className="col-12 mb-1">
                                        <label className="form-label">
                                            Name <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('name')}
                                            type="text"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.name?.message),
                                            })}
                                        />
                                        {errors.name?.message && <div className="error">{errors.name?.message}</div>}
                                    </div>
                                    <div className="col-12 mb-1">
                                        <label className="form-label">Description</label>
                                        <textarea
                                            {...register('desc')}
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.desc?.message),
                                            })}
                                            rows={3}
                                        />
                                        {errors.desc?.message && <div className="error">{errors.desc?.message}</div>}
                                    </div>
                                    <div className="col-12 mb-1">
                                        <label className="form-label">
                                            Display Order <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('display_order')}
                                            type="number"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.display_order?.message),
                                            })}
                                        />
                                        {errors.display_order?.message && (
                                            <div className="error">{errors.display_order?.message}</div>
                                        )}
                                    </div>
                                    <div className="col-12 mb-1">
                                        <label className="form-label">
                                            Status <span className="error">*</span>
                                        </label>
                                        <Controller
                                            name="status_id"
                                            control={control}
                                            render={({ field }) => (
                                                <Select
                                                    {...field}
                                                    options={statusOptions}
                                                    className={classNames('react-select', {
                                                        'is-invalid': Boolean(errors.status_id?.message),
                                                    })}
                                                    classNamePrefix="select"
                                                    styles={{
                                                        control: (baseStyles, state) =>
                                                            getSelectStyle(
                                                                baseStyles,
                                                                state,
                                                                Boolean(errors.status_id?.message)
                                                            ),
                                                    }}
                                                    value={statusOptions.find((c) => c.value === field.value)}
                                                    onChange={(val) => field.onChange(val?.value)}
                                                />
                                            )}
                                        />
                                        {errors.status_id?.message && (
                                            <div className="error">{errors.status_id?.message}</div>
                                        )}
                                    </div>
                                </div>
                            </div>
                            <div className="modal-footer">
                                <button
                                    type="button"
                                    className="btn btn-outline-secondary"
                                    onClick={() => changeShow(false)}
                                >
                                    Cancel
                                </button>
                                <UpdateButton isLoading={isLoading} btnText="Save" hasDivWrap={false} />
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}
        </>
    );
}
