import { gql } from 'graphql-request';

export const TEMPLATE_COMPONENT_LIST = gql`
    query Template_component_list($page: Int!, $limit: Int!, $filters: [String!], $sort: String, $search: String) {
        template_component_list(body: { page: $page, limit: $limit, filters: $filters, sort: $sort, search: $search }) {
            totalCount
            totalPages
            currentPage
            data {
                id
                name
                desc
                status_id
            }
        }
    }
`;

export const TEMPLATE_COMPONENT_CREATE = gql`
    mutation Template_component_create($body: TemplateComponentSaveInputDto!) {
        template_component_create(body: $body) {
            id
        }
    }
`;

export const TEMPLATE_COMPONENT_UPDATE = gql`
    mutation Template_component_update($id: Int!, $body: TemplateComponentSaveInputDto!) {
        template_component_update(id: $id, body: $body) {
            id
        }
    }
`;

export const TEMPLATE_COMPONENT_DELETE = gql`
    mutation Template_component_delete($id: Int!) {
        template_component_delete(id: $id)
    }
`;

export const TEMPLATE_COMPONENT_VIEW = gql`
    query Template_component_view($id: Int!) {
        template_component_view(id: $id) {
            id
            name
            desc
            status_id
            file {
                id
                file_name
                file_url
            }
        }
    }
`;
