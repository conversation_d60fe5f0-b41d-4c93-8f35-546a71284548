import { useGraphQLMutation } from 'hooks/useGraphQLMutation';
import { showToast } from 'utils/common';
import { ERROR_MESSAGE, SUCCESS_MESSAGE } from 'constants/common';
import ModalConfirm from 'components/partials/ModalConfirm';
import { BACKUP_WEBSITES_RESTORE } from 'services/WebsiteService';

interface ModalRestoreProps {
    show: boolean;
    onClose: () => void;
    websiteId: number;
    onBackupSuccess?: () => void;
}

export default function ModalRestore({ show, onClose, websiteId, onBackupSuccess }: ModalRestoreProps) {
    const { mutateAsync: restoreBackup, status } = useGraphQLMutation(
        BACKUP_WEBSITES_RESTORE,
        'Backup_websites_update_restore_status',
        {
            onSuccess: () => {
                showToast(true, [SUCCESS_MESSAGE]);
                onBackupSuccess?.();
                onClose();
            },
        }
    );

    const isCreating = status === 'pending';

    return (
        <ModalConfirm
            show={show}
            text="Are you sure you want to restore this backup for the website?"
            btnDisabled={isCreating}
            changeShow={onClose}
            submitAction={() => restoreBackup({ id: websiteId })}
        />
    );
}
