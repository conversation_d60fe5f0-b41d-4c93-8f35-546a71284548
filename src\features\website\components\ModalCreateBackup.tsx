import { useGraphQLMutation } from 'hooks/useGraphQLMutation';
import { MUTATION_REQUEST_BACKUP } from 'services/WebsiteStyleService';
import { showToast } from 'utils/common';
import { ERROR_MESSAGE, SUCCESS_MESSAGE } from 'constants/common';
import ModalConfirm from 'components/partials/ModalConfirm';

interface ModalCreateBackupProps {
    show: boolean;
    onClose: () => void;
    websiteId: number;
    onBackupSuccess?: () => void;
}

export default function ModalCreateBackup({ show, onClose, websiteId, onBackupSuccess }: ModalCreateBackupProps) {
    const { mutateAsync: createBackup, status } = useGraphQLMutation(
        MUTATION_REQUEST_BACKUP,
        'Websites_request_backup',
        {
            onSuccess: () => {
                showToast(true, [SUCCESS_MESSAGE]);
                onBackupSuccess?.();
                onClose();
            },
        }
    );

    const isCreating = status === 'pending';

    return (
        <ModalConfirm
            show={show}
            text="Are you sure you want to create a backup for this website?"
            btnDisabled={isCreating}
            changeShow={onClose}
            submitAction={() => createBackup({ id: websiteId })}
        />
    );
}
