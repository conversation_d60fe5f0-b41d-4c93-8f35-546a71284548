import { keepPreviousData } from '@tanstack/react-query';
import ContentHeader from 'components/partials/ContentHeader';
import ModalConfirm from 'components/partials/ModalConfirm';
import Spinner from 'components/partials/Spinner';
import { QUERY_KEY, SUCCESS_MESSAGE } from 'constants/common';
import useQueryParams from 'hooks/useQueryParams';
import isUndefined from 'lodash/isUndefined';
import omitBy from 'lodash/omitBy';
import { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { useAuthStore } from 'stores/authStore';
import { generateFilters, showToast } from 'utils/common';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import ListSubPlan from '../components/ListSubPlan';
import { SUBSCRIPTION_PLAN_DELETE, SUBSCRIPTION_PLAN_LIST } from 'services/SubscriptionPlanService';
import {
    SearchSubscriptionPlanParam,
    subscriptionPlanFilterConfig,
    SubscriptionPlanQuery,
} from '../../../types/SubcriptionPlan';
import SearchSubPlanForm from '../components/SearchSubPlanForm';

export default function SubscriptionPlanList() {
    const user = useAuthStore((state) => state.user);
    const { t } = useTranslation();

    const { queryParams } = useQueryParams<SearchSubscriptionPlanParam>();
    const paramConfig: SearchSubscriptionPlanParam = omitBy(
        {
            search: queryParams.search,
            status_id: queryParams.status_id,
            type_id: queryParams.type_id,
        },
        isUndefined
    );

    const filters = generateFilters(paramConfig, subscriptionPlanFilterConfig);

    const [showDelete, setShowDelete] = useState(false);
    const [itemId, setItemId] = useState(0);

    const { data, isLoading, isRefetching, refetch } = useGraphQLQuery<SubscriptionPlanQuery>(
        [QUERY_KEY.SUBSCRIPTION_PLANS, paramConfig, user, filters],
        SUBSCRIPTION_PLAN_LIST,
        {
            search: queryParams.search,
            filters: filters.length > 0 ? filters : undefined,
            sort: 'display_order:ASC',
        },
        '',
        {
            enabled: !!user,
            placeholderData: keepPreviousData,
        }
    );

    const deleteMutation = useGraphQLMutation(SUBSCRIPTION_PLAN_DELETE, '', {
        onSuccess: () => {
            showToast(true, [SUCCESS_MESSAGE]);
            setShowDelete(false);
            setItemId(0);
            refetch();
        },
    });

    const handleDelete = (id: number) => {
        deleteMutation.reset();
        setItemId(id);
        setShowDelete(true);
    };

    const deleteItem = async () => {
        if (itemId) {
            deleteMutation.mutate({ id: itemId });
        }
    };

    return (
        <>
            <Helmet>
                <title>{t('subscription_plan.single')}</title>
            </Helmet>
            <ContentHeader title={t('subscription_plan.single')} />
            <div className="content-body">
                <div className="col-12">
                    <SearchSubPlanForm isLoading={isLoading || isRefetching} />
                    {(isLoading || isRefetching) && <Spinner />}
                    {!isLoading && !isRefetching && (
                        <div className="card">
                            <ListSubPlan items={data?.subscription_plan_list ?? []} handleDelete={handleDelete} />
                        </div>
                    )}
                    <ModalConfirm
                        show={showDelete}
                        text={t('confirm.delete', { data: 'subscription plan' })}
                        btnDisabled={deleteMutation.isPending}
                        changeShow={(s: boolean) => setShowDelete(s)}
                        submitAction={deleteItem}
                    />
                </div>
            </div>
        </>
    );
}
