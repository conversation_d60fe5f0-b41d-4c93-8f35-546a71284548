import UserLink from 'components/partials/UserLink';
import { Edit, Trash2 } from 'react-feather';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import Component from 'types/Component';
import { ItemStatus, ItemStatusNames } from 'types/common/Item';
import { genTableIndex, getFieldHtml } from 'utils/common';
import { Paging } from '../../../types/common';

interface IProps {
    items: Component[];
    paging: Paging;
    handleDelete: (id: number) => void;
}

export default function ListComponent({ items, paging, handleDelete }: Readonly<IProps>) {
    const { t } = useTranslation();

    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="text-center">No</th>
                        <th>Name</th>
                        <th>Description</th>
                        <th className="text-center">Status</th>
                        <th className="thAction1"></th>
                    </tr>
                </thead>
                <tbody>
                    {items.map((item: Component, index: number) => (
                        <tr key={item.id}>
                            <td className="text-center">{genTableIndex(index, paging.limit, paging.current_page)}</td>
                            <td>
                                <UserLink to={`/component/edit/${item.id}`} name={item.name} />
                            </td>
                            <td>{item.desc}</td>
                            <td className="text-center">{getFieldHtml(ItemStatusNames, item.status_id, t)}</td>
                            <td className="text-center">
                                {item.status_id !== ItemStatus.ACTIVE && (
                                    <button
                                        type="button"
                                        title={t('delete')}
                                        className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                        onClick={() => handleDelete(item.id!)}
                                    >
                                        <Trash2 size={14} />
                                    </button>
                                )}
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
