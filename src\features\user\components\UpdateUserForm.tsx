import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import UpdateButton from 'components/partials/UpdateButton';
import UploadImage from 'components/partials/UploadImage';
import {
    DEFAULT_VALUE,
    ERROR_MESSAGE,
    QUERY_KEY,
    SUCCESS_MESSAGE,
    optionConstantDefault,
    optionModelDefault,
} from 'constants/common';
import { includes } from 'lodash';
import { useEffect, useMemo, useState } from 'react';
import { Eye, EyeOff } from 'react-feather';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Link, useNavigate } from 'react-router-dom';
import Select from 'react-select';
import FileService from 'services/FileService';
import User, { AuthUpdateRes, Gender, GenderNames, UserCreate, UserRole, UserUpdate } from 'types/User';
import { ItemStatus, ItemStatusNames, ItemStatusNames2, SelectOption } from 'types/common/Item';
import { isValidImageFile, selectItem, showToast } from 'utils/common';
import { formatInputDateTime } from 'utils/date';
import * as yup from 'yup';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import { AUTH_UPDATE, USER_CREATE, USER_UPDATE } from '../../../services/UserService';
import omitBy from 'lodash/omitBy';
import omit from 'lodash/omit';
import isNull from 'lodash/isNull';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { INDUSTRY_LIST } from '../../../services/IndustryService';
import { keepPreviousData } from '@tanstack/react-query';
import { IndustryQuery } from '../../../types/Industry';

interface IProps {
    id: string;
    roleId: UserRole | number;
    user?: User;
    isProfile?: boolean;
    type?: string;
}

export default function UpdateUserForm({ id, roleId, user, isProfile = false, type }: Readonly<IProps>) {
    const [showPass, setShowPass] = useState(false);
    const [showRePass, setShowRePass] = useState(false);
    const [avatar, setAvatar] = useState(user?.avatar?.file_url ?? DEFAULT_VALUE.IMAGE);
    const [fileAvatar, setFileAvatar] = useState<File>();
    const navigate = useNavigate();
    const { t } = useTranslation();

    const { data: industryData } = useGraphQLQuery<IndustryQuery>(
        [QUERY_KEY.INDUSTRIES],
        INDUSTRY_LIST,
        {
            filters: [`status_id:=(${ItemStatus.ACTIVE})`],
        },
        '',
        {
            enabled: !!roleId && roleId === UserRole.CUSTOMER,
            placeholderData: keepPreviousData,
        }
    );

    const industryOptions = useMemo(() => {
        const options: SelectOption[] = [optionConstantDefault];
        industryData?.industry_list.forEach((dp) => {
            options.push({
                value: dp.id!,
                label: dp.name,
            });
        });
        return options;
    }, [industryData]);

    const yupObj = {
        first_name: yup.string().required(t('error.required')).max(255).trim(),
        last_name: yup.string().required(t('error.required')).max(255).trim(),
        phone_number: yup
            .string()
            .required('Please enter your phone number.')
            .max(20)
            .trim()
            .matches(/^(\d+)?$/, 'Please enter numbers only'),
        email: yup
            .string()
            .required('Please enter your email.')
            .max(254)
            .trim()
            .matches(/^[^\s@]+@[^\s@]+\.[^\s@]+$/, 'Please enter a valid email address.'),
        gender_id: yup.number().min(1, t('error.required')),
        status_id: yup.number().min(1, t('error.required')),
    };

    if (roleId === UserRole.CUSTOMER) {
        Object.assign(yupObj, {
            address: yup.string().max(255).trim(),
            birthday: yup.string().trim(),
            industry_id: yup
                .number()
                .nullable()
                .transform((value) => (isNaN(value) ? null : value)),
        });
    }

    if (!id) {
        Object.assign(yupObj, {
            password: yup
                .string()
                .required('Please enter a password.')
                .trim()
                .min(12, 'Use 12 characters or more for your password.')
                .matches(/[A-Z]/, 'The password should contain at least one uppercase letter.')
                .matches(/[a-z]/, 'The password should contain at least one lowercase letter.')
                .matches(/[0-9]/, 'The password should contain at least one numeric character.')
                .matches(/[^A-Za-z0-9]/, 'The password should contain at least one special character.'),
            confirm_password: yup
                .string()
                .required('Please enter a password.')
                .trim()
                .min(12, 'Use 12 characters or more for your password.')
                .oneOf([yup.ref('password'), null], 'The passwords do not match.'),
        });
    }

    const schema = yup.object(yupObj).required();
    const {
        register,
        handleSubmit,
        setValue,
        control,
        reset,
        formState: { isSubmitting, errors },
    } = useForm<User>({
        resolver: yupResolver(schema),
    });

    useEffect(() => {
        if (roleId > 0) {
            if (id && user) {
                reset({
                    ...user,
                    gender_id: user.gender_id ?? Gender.OTHER,
                    birthday: user.birthday ? formatInputDateTime(new Date(Number(user.birthday)).toISOString()) : '',
                });
                setAvatar(user?.avatar?.file_url ?? DEFAULT_VALUE.IMAGE);
            } else {
                setValue('status_id', ItemStatus.ACTIVE);
                setValue('gender_id', Gender.OTHER);
            }
        }
    }, [id, user, roleId, reset, setValue]);

    const userCreateMutation = useGraphQLMutation<UserCreate, { body: Partial<User> }>(USER_CREATE, '', {
        onSuccess() {
            showToast(true, [SUCCESS_MESSAGE]);
            setTimeout(() => {
                navigate(`/user/list/${type}`);
            }, 2000);
        },
    });

    const userUpdateMutation = useGraphQLMutation<UserUpdate, { id: number; body: Partial<User> }>(USER_UPDATE, '', {
        onSuccess() {
            showToast(true, [SUCCESS_MESSAGE]);
            setTimeout(() => {
                navigate(`/user/list/${type}`);
            }, 2000);
        },
    });

    const authUpdateMutation = useGraphQLMutation<AuthUpdateRes, { body: Partial<User> }>(AUTH_UPDATE, '', {
        onSuccess() {
            showToast(true, [SUCCESS_MESSAGE]);
            setTimeout(() => {
                navigate(`/user/profile`);
            }, 2000);
        },
    });

    const onChangeFile = (file: File) => {
        if (!isValidImageFile(file)) {
            showToast(false, [t('error.chooseImage')]);
            return;
        }
        setFileAvatar(file);
        setAvatar(URL.createObjectURL(file));
    };

    const handleUploadAvatar = async () => {
        let avatarTmp = avatar;
        let avatarId = 0;
        if (fileAvatar) {
            const results = await FileService.upload(fileAvatar);
            if (results.upload) {
                avatarId = results.upload.id;
                avatarTmp = results.upload.url;
                setAvatar(avatarTmp);
            } else {
                showToast(false, [ERROR_MESSAGE]);
            }
            setFileAvatar(undefined);
        }
        return avatarId;
    };

    const onSubmit = async (formData: User) => {
        delete formData.id;
        const data = omit(formData, [
            'created_at',
            'created_by',
            'updated_at',
            'updated_by',
            'uid',
            'avatar',
            'groups',
        ]);
        if (roleId === UserRole.CUSTOMER && data.birthday) {
            data.birthday = new Date(data.birthday).toISOString();
        } else delete data.birthday;
        const avatarIdTmp = await handleUploadAvatar();
        if (avatarIdTmp) data.avatar_id = avatarIdTmp;
        let dataUpdate = omitBy(data, isNull);
        dataUpdate.role_id = roleId;
        if (roleId !== UserRole.CUSTOMER) delete dataUpdate.industry_id;
        else dataUpdate.industry_id = dataUpdate.industry_id || null;
        if (!dataUpdate.address) dataUpdate.address = null;
        if (isProfile) {
            dataUpdate = omit(dataUpdate, ['status_id']);
            authUpdateMutation.mutate({ body: dataUpdate });
        } else if (!id) {
            userCreateMutation.mutate({ body: dataUpdate });
        } else {
            userUpdateMutation.mutate({ id: Number(id), body: dataUpdate });
        }
    };

    return (
        <div className="card">
            <div className="card-header border-bottom">
                <h4 className="card-title">{t('baseInformation')}</h4>
            </div>
            <div className="card-body py-2 my-25">
                <form className="validate-form pt-50" onSubmit={handleSubmit(onSubmit)}>
                    <div className="row">
                        <div className="col-12 col-sm-6">
                            <UploadImage id={1} image={avatar} label={t('uploadAvatar')} onChangeFile={onChangeFile} />
                        </div>
                        <div className="col-12 col-sm-6"></div>
                        <div className="col-12 col-sm-4 mb-1">
                            <label className="form-label">
                                First Name <span className="error">*</span>
                            </label>
                            <input
                                {...register('first_name')}
                                type="text"
                                className={classNames('form-control', {
                                    'is-invalid': Boolean(errors.first_name?.message),
                                })}
                            />
                            <span className="error">{errors.first_name?.message}</span>
                        </div>
                        <div className="col-12 col-sm-4 mb-1">
                            <label className="form-label">
                                Last Name <span className="error">*</span>
                            </label>
                            <input
                                {...register('last_name')}
                                type="text"
                                className={classNames('form-control', {
                                    'is-invalid': Boolean(errors.last_name?.message),
                                })}
                            />
                            <span className="error">{errors.last_name?.message}</span>
                        </div>
                        <div className="col-12 col-sm-4 mb-1">
                            <label className="form-label">
                                {t('email')} <span className="error">*</span>
                            </label>
                            <input
                                {...register('email')}
                                type="text"
                                className={classNames('form-control', {
                                    'is-invalid': Boolean(errors.email?.message),
                                })}
                                readOnly={!!user && roleId !== UserRole.ADMIN}
                            />
                            <span className="error">{errors.email?.message}</span>
                        </div>
                        <div className="col-12 col-sm-4 mb-1">
                            <label className="form-label">
                                {t('phoneNumber')} <span className="error">*</span>
                            </label>
                            <input
                                {...register('phone_number')}
                                type="text"
                                className={classNames('form-control', {
                                    'is-invalid': Boolean(errors.phone_number?.message),
                                })}
                            />
                            <span className="error">{errors.phone_number?.message}</span>
                        </div>
                        <div className="col-12 col-sm-4 mb-1">
                            <label className="form-label">{t('statusName')}</label>
                            <select
                                {...register('status_id', { valueAsNumber: true })}
                                className="form-select"
                                disabled={isProfile}
                            >
                                {selectItem(roleId === UserRole.ADMIN ? ItemStatusNames : ItemStatusNames2, t, true)}
                            </select>
                        </div>
                        <div className="col-12 col-sm-4 mb-1">
                            <label className="form-label">{t('genderName')}</label>
                            <select {...register('gender_id', { valueAsNumber: true })} className="form-select">
                                {selectItem(GenderNames, t, true)}
                            </select>
                        </div>
                        {includes([UserRole.CUSTOMER], roleId) && (
                            <>
                                <div className="col-12 col-sm-4 mb-1">
                                    <label className="form-label">{t('birthday')}</label>
                                    <input {...register('birthday')} type="date" className="form-control" />
                                </div>
                                <div className={`col-12 mb-1 ${roleId === UserRole.ADMIN ? 'col-sm-8' : 'col-sm-4'}`}>
                                    <label className="form-label">{t('address')}</label>
                                    <input
                                        {...register('address')}
                                        type="text"
                                        className={classNames('form-control')}
                                    />
                                </div>
                                <div className="col-12 col-sm-4 mb-1">
                                    <label className="form-label">Industry</label>
                                    {industryOptions && (
                                        <Controller
                                            control={control}
                                            name="industry_id"
                                            defaultValue={user?.industry_id ?? 0}
                                            render={({ field: { onChange, value, ...field } }) => {
                                                const defaultValue =
                                                    industryOptions.find((option) => option.value === value) ??
                                                    optionModelDefault;
                                                return (
                                                    <Select
                                                        {...field}
                                                        options={industryOptions}
                                                        onChange={(option) => {
                                                            onChange(option?.value ?? 0);
                                                        }}
                                                        value={defaultValue}
                                                        isClearable
                                                    />
                                                );
                                            }}
                                        />
                                    )}
                                </div>
                            </>
                        )}
                        {!id && (
                            <>
                                <div className="col-12 col-sm-4 mb-1">
                                    <label className="form-label">
                                        {t('password')} <span className="error">*</span>
                                    </label>
                                    <div className="input-group input-group-merge form-password-toggle">
                                        <input
                                            {...register('password')}
                                            className={classNames('form-control', 'form-control-merge', {
                                                error: Boolean(errors.password?.message),
                                            })}
                                            type={showPass ? 'text' : 'password'}
                                            placeholder="············"
                                        />
                                        <span
                                            className="input-group-text cursor-pointer"
                                            onClick={() => setShowPass((prevShowPass) => !prevShowPass)}
                                            style={{
                                                borderColor: errors.password?.message ? '#ea5455' : '#d8d6de',
                                            }}
                                        >
                                            {showPass ? <EyeOff size={14} /> : <Eye size={14} />}
                                        </span>
                                    </div>
                                    <span className="error">{errors.password?.message}</span>
                                </div>
                                <div className="col-12 col-sm-4 mb-1">
                                    <label className="form-label">
                                        {t('rePassword')} <span className="error">*</span>
                                    </label>
                                    <div className="input-group input-group-merge form-password-toggle">
                                        <input
                                            {...register('confirm_password')}
                                            className={classNames('form-control', 'form-control-merge', {
                                                error: Boolean(errors.confirm_password?.message),
                                            })}
                                            type={showRePass ? 'text' : 'password'}
                                            placeholder="············"
                                        />
                                        <span
                                            className="input-group-text cursor-pointer"
                                            onClick={() => setShowRePass((prevShowRePass) => !prevShowRePass)}
                                            style={{
                                                borderColor: errors.password?.message ? '#ea5455' : '#d8d6de',
                                            }}
                                        >
                                            {showRePass ? <EyeOff size={14} /> : <Eye size={14} />}
                                        </span>
                                    </div>
                                    <span className="error">{errors.confirm_password?.message}</span>
                                </div>
                            </>
                        )}
                        <div className="col-12">
                            <div className="float-end">
                                <Link
                                    className="btn btn-outline-primary waves-effect waves-float waves-light mt-1 ms-2"
                                    to={`/user/list/${type}`}
                                >
                                    Cancel
                                </Link>
                            </div>
                            <UpdateButton btnText={t('update')} isLoading={isSubmitting} btnClass={['mt-1']} />
                        </div>
                    </div>
                </form>
            </div>
        </div>
    );
}
