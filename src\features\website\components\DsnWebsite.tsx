import { useState } from 'react';
import ModalContent from 'components/partials/ModalContent';
import {
    CLOUDFLARE_GET_DNS_RECORDS,
    CLOUDFLARE_CREATE_DNS_RECORD,
    CLOUDFLARE_UPDATE_DNS_RECORD,
    CLOUDFLARE_DELETE_DNS_RECORD,
} from 'services/CloudflareService';
import { QUERY_KEY, SUCCESS_MESSAGE } from 'constants/common';
import ModalDNSForm from './ModalDNSForm';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import { useGraphQLMutation } from 'hooks/useGraphQLMutation';
import { Trash } from 'react-feather';
import { CloudflareDNSFormData, CloudflareDNSRecord, WebsiteDnsQuery } from 'types/Website';
import { showToast } from 'utils/common';
import ModalConfirm from 'components/partials/ModalConfirm';
import { useTranslation } from 'react-i18next';

interface ModalDNSRecordsProps {
    domain: string;
    show: boolean;
    onClose: () => void;
}

export default function ModalDNSRecords({ domain, show, onClose }: ModalDNSRecordsProps) {
    const { t } = useTranslation('');
    const [formOpen, setFormOpen] = useState(false);
    const [mode, setMode] = useState<'create' | 'update'>('create');
    const [current, setCurrent] = useState<CloudflareDNSRecord>();
    const [confirmDeleteId, setConfirmDeleteId] = useState<string | null>(null);

    const { data, refetch } = useGraphQLQuery<WebsiteDnsQuery>(
        [QUERY_KEY.DNS_RECORDS, domain],
        CLOUDFLARE_GET_DNS_RECORDS,
        { domain },
        '',
        {
            enabled: show,
        }
    );

    const createMutation = useGraphQLMutation(CLOUDFLARE_CREATE_DNS_RECORD, 'cloudflare_create_dns_record', {
        onSuccess: () => {
            showToast(true, [SUCCESS_MESSAGE]);
            refetch();
            setFormOpen(false);
        },
    });

    const updateMutation = useGraphQLMutation(CLOUDFLARE_UPDATE_DNS_RECORD, 'cloudflare_update_dns_record', {
        onSuccess: () => {
            showToast(true, [SUCCESS_MESSAGE]);
            refetch();
            setFormOpen(false);
        },
    });

    const deleteMutation = useGraphQLMutation(CLOUDFLARE_DELETE_DNS_RECORD, 'cloudflare_delete_dns_record', {
        onSuccess: () => {
            showToast(true, [SUCCESS_MESSAGE]);
            refetch();
        },
    });

    const openForm = (formMode: 'create' | 'update', record?: CloudflareDNSRecord) => {
        setMode(formMode);
        setCurrent(record!);
        setFormOpen(true);
    };

    const handleSubmit = (record: CloudflareDNSFormData) => {
        const input = { ...record, domain };
        if (mode === 'create') {
            createMutation.mutate({ input });
        } else {
            updateMutation.mutate({ input });
        }
    };

    const handleDelete = (recordId: string) => {
        setConfirmDeleteId(recordId);
    };

    const confirmDelete = () => {
        if (confirmDeleteId) {
            deleteMutation.mutate({ domain, recordId: confirmDeleteId });
            setConfirmDeleteId(null);
        }
    };

    return (
        <>
            <ModalContent
                show={show}
                changeShow={onClose}
                title={`DNS Records for ${domain}`}
                content={
                    <>
                        <div className="flex justify-between mb-2">
                            <button className="btn btn-primary" onClick={() => openForm('create')}>
                                Create Record
                            </button>
                        </div>
                        <div className="overflow-auto">
                            <table className="table table-hover table-bordered text-sm">
                                <thead>
                                    <tr>
                                        <th className="text-center">No</th>
                                        <th>Type</th>
                                        <th>Name</th>
                                        <th>Content</th>
                                        <th>TTL</th>
                                        <th>Priority</th>
                                        <th>Proxy</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {data?.cloudflare_get_dns_records?.map((r: CloudflareDNSRecord, index: number) => (
                                        <tr
                                            key={r.id}
                                            className="hover:bg-gray-100 cursor-pointer"
                                            onClick={() => openForm('update', r)}
                                        >
                                            <td className="text-center">{index + 1}</td>
                                            <td>{r.type}</td>
                                            <td>{r.name}</td>
                                            <td>{r.content}</td>
                                            <td>{r.ttl ?? '-'}</td>
                                            <td>{r.priority ?? '-'}</td>
                                            <td>{r.proxied ? 'Yes' : 'No'}</td>
                                            <td className="text-center">
                                                <button
                                                    type="button"
                                                    title={t('delete')}
                                                    className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        handleDelete(r.id!.toString());
                                                    }}
                                                >
                                                    <Trash size={14} />
                                                </button>
                                            </td>
                                        </tr>
                                    ))}

                                    {!data?.cloudflare_get_dns_records?.length && (
                                        <tr>
                                            <td colSpan={8} className="text-center text-gray-400 italic py-4">
                                                No DNS records found.
                                            </td>
                                        </tr>
                                    )}
                                </tbody>
                            </table>
                        </div>
                    </>
                }
            />

            <ModalDNSForm
                show={formOpen}
                onClose={() => setFormOpen(false)}
                mode={mode}
                initialData={current}
                domain={domain}
                onSubmit={handleSubmit}
                isLoading={createMutation.status === 'pending' || updateMutation.status === 'pending'}
            />
            <ModalConfirm
                show={!!confirmDeleteId}
                text="Are you sure you want to delete this DNS record?"
                btnDisabled={deleteMutation.status === 'pending'}
                changeShow={() => setConfirmDeleteId(null)}
                submitAction={confirmDelete}
            />
        </>
    );
}
