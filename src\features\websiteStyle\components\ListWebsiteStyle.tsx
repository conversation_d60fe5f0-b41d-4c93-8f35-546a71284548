import { Edit, Trash2 } from 'react-feather';
import WebsiteStyle from 'types/WebsiteStyle';
import { ItemStatus, ItemStatusNames2 } from 'types/common/Item';
import { getFieldHtml } from 'utils/common';
import { useTranslation } from 'react-i18next';

interface IProps {
    items: WebsiteStyle[];
    handleDelete: (id: number) => void;
    handleEdit: (id: number) => void;
}

export default function ListWebsiteStyle({ items, handleDelete, handleEdit }: Readonly<IProps>) {
    const { t } = useTranslation();

    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="text-center">No</th>
                        <th>Name</th>
                        <th className="text-center">Status</th>
                        <th className="thAction1"></th>
                    </tr>
                </thead>
                <tbody>
                    {items.map((item: WebsiteStyle) => (
                        <tr key={item.id}>
                            <td className="text-center">{item.display_order}</td>
                            <td>
                                <span className="text-primary cursor-pointer" onClick={() => handleEdit(item.id!)}>
                                    {item.name}
                                </span>
                            </td>
                            <td className="text-center">{getFieldHtml(ItemStatusNames2, item.status_id, t)}</td>
                            <td className="text-center">
                                {item.status_id !== ItemStatus.ACTIVE && (
                                    <button
                                        type="button"
                                        title={t('delete')}
                                        className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                        onClick={() => handleDelete(item.id!)}
                                    >
                                        <Trash2 size={14} />
                                    </button>
                                )}
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
