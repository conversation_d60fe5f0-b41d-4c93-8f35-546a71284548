import { FILTER_CONDITIONS } from 'constants/common';
import { baseFilterConfig, BaseModel, DataList, FilterConfig } from './common';

export interface SmtpConfig {
    id?: number;
    host: string;
    port: number;
    ssl: boolean;
    username: string;
    password: string;
    customer_id?: number;
}

export default interface Smtp extends BaseModel {
    customer_id: number;
    smtp_config: SmtpConfig;
}

export interface SmtpQuery {
    smtps_list: DataList<Smtp>;
}

export const smtpFilterConfig: FilterConfig = {
    ...baseFilterConfig,
    customer_id: { key: 'customer_id', operator: FILTER_CONDITIONS.EQUAL },
};
