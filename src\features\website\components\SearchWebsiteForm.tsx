import SearchForm from 'components/partials/SearchForm';
import { SearchField } from 'types/common/Search';

interface IProps {
    isLoading: boolean;
}

export default function SearchWebsiteForm({ isLoading }: Readonly<IProps>) {
    const fields: SearchField[] = [{ name: 'search', type: 'text', label: 'Domain', wrapClassName: 'col-md-4 col-12' }];

    return <SearchForm fields={fields} isLoading={isLoading} />;
}
