import { useQuery, UseQueryOptions, UseQueryResult } from '@tanstack/react-query';
import { DocumentNode, print } from 'graphql';
import { Variables } from 'graphql-request';
import { axiosGraphQLClient } from '../services/AxiosGraphQLClient';

type QueryKeyT = readonly unknown[];

/* eslint-disable @tanstack/query/exhaustive-deps */
export function useGraphQLQuery<TData = unknown, TVariables extends Variables = Variables>(
    queryKey: QueryKeyT,
    query: DocumentNode | string,
    variables?: TVariables,
    operationName?: string,
    options?: Omit<UseQueryOptions<TData, Error, TData, QueryKeyT>, 'queryKey' | 'queryFn'>,
    isShowToast = true,
    customHeaders?: Record<string, string>
): UseQueryResult<TData, Error> {
    return useQuery<TData, Error, TData, QueryKeyT>({
        queryKey,
        queryFn: () => {
            const queryString = typeof query === 'string' ? query : print(query);
            return axiosGraphQLClient.request<TData, TVariables>(
                queryString,
                variables,
                operationName,
                isShowToast,
                customHeaders
            );
        },
        ...options,
    });
}
