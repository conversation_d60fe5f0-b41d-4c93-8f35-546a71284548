import React, { useState, useRef, useLayoutEffect } from 'react';
import { useTranslation } from 'react-i18next';
import classNames from 'classnames';
import { toggleModalOpen } from 'utils/common';
import { MasterTagImportResult } from 'types/MasterTag';

interface ModalMasterTagImportProps {
    show: boolean;
    isLoading: boolean;
    changeShow: (show: boolean) => void;
    submitAction: (file: File) => void;
    importResult?: MasterTagImportResult;
}

export default function ModalMasterTagImport({
    show,
    isLoading,
    changeShow,
    submitAction,
    importResult,
}: ModalMasterTagImportProps) {
    const { t } = useTranslation();
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [dragActive, setDragActive] = useState(false);
    const fileInputRef = useRef<HTMLInputElement>(null);

    useLayoutEffect(() => toggleModalOpen(show), [show]);

    const handleClose = () => {
        setSelectedFile(null);
        changeShow(false);
    };

    const handleFileSelect = (file: File) => {
        if (file && file.type === 'text/csv') {
            setSelectedFile(file);
        } else {
            alert('Please select a valid CSV file');
        }
    };

    const handleDrag = (e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        if (e.type === 'dragenter' || e.type === 'dragover') {
            setDragActive(true);
        } else if (e.type === 'dragleave') {
            setDragActive(false);
        }
    };

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setDragActive(false);

        if (e.dataTransfer.files && e.dataTransfer.files[0]) {
            handleFileSelect(e.dataTransfer.files[0]);
        }
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            handleFileSelect(e.target.files[0]);
        }
    };

    const handleSubmit = () => {
        if (selectedFile) {
            submitAction(selectedFile);
        }
    };

    return (
        <>
            <div
                className={classNames('modal fade text-start modal-primary', { show })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-dialog-centered modal-lg">
                    <div
                        className="modal-content"
                        style={{
                            borderRadius: '16px',
                            border: 'none',
                            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.1)',
                            overflow: 'hidden',
                        }}
                    >
                        <div
                            className="modal-header"
                            style={{
                                padding: '2rem 2.5rem 1.5rem',
                                fontSize: '90%',
                                borderBottom: '1px solid #f0f0f0',
                                backgroundColor: '#fafafa',
                            }}
                        >
                            <div>
                                <h4
                                    className="modal-title mb-1"
                                    style={{
                                        fontWeight: '600',
                                        color: '#1a1a1a',
                                        fontSize: '1.5rem',
                                    }}
                                >
                                    Import Master Tags
                                </h4>
                                <p className="text-muted mb-0" style={{ fontSize: '0.9rem' }}>
                                    Upload a CSV file to import multiple master tags at once
                                </p>
                            </div>
                            <button
                                type="button"
                                className="btn-close"
                                onClick={handleClose}
                                style={{
                                    padding: '0.75rem',
                                    margin: '-0.75rem -0.75rem -0.75rem auto',
                                }}
                            />
                        </div>
                        <div className="modal-body" style={{ padding: '2.5rem' }}>
                            {!importResult ? (
                                <>
                                    <div
                                        className={classNames(
                                            'border rounded-3 text-center drag-drop-area position-relative',
                                            dragActive ? 'border-primary bg-light active' : 'border-2 border-dashed'
                                        )}
                                        onDragEnter={handleDrag}
                                        onDragLeave={handleDrag}
                                        onDragOver={handleDrag}
                                        onDrop={handleDrop}
                                        style={{
                                            minHeight: '200px',
                                            display: 'flex',
                                            flexDirection: 'column',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            borderRadius: '16px',
                                            transition: 'all 0.3s ease',
                                            backgroundColor: dragActive ? '#f8f9ff' : '#fafbfc',
                                            borderColor: dragActive ? '#007bff' : '#e9ecef',
                                            cursor: 'pointer',
                                        }}
                                        onClick={() => !selectedFile && fileInputRef.current?.click()}
                                    >
                                        {selectedFile ? (
                                            <div className="py-3">
                                                <h5 className="mb-2" style={{ fontWeight: '600', color: '#1a1a1a' }}>
                                                    {selectedFile.name}
                                                </h5>
                                                <p className="text-muted mb-3" style={{ fontSize: '0.95rem' }}>
                                                    Size: {Math.round(selectedFile.size / 1024)} KB
                                                </p>
                                                <button
                                                    className="btn btn-outline-secondary rounded-pill px-4"
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        setSelectedFile(null);
                                                    }}
                                                    style={{
                                                        fontWeight: '500',
                                                        fontSize: '0.9rem',
                                                    }}
                                                >
                                                    Remove File
                                                </button>
                                            </div>
                                        ) : (
                                            <div className="py-2">
                                                <h5 className="mb-2" style={{ fontWeight: '600', color: '#1a1a1a' }}>
                                                    Drop your CSV file here
                                                </h5>
                                                <p className="text-muted mb-2" style={{ fontSize: '0.95rem' }}>
                                                    or click to browse from your computer
                                                </p>
                                                <div className="d-flex flex-column align-items-center gap-2">
                                                    <button
                                                        className="btn btn-primary rounded-pill px-4 py-2"
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            fileInputRef.current?.click();
                                                        }}
                                                        style={{
                                                            fontWeight: '500',
                                                            fontSize: '0.95rem',
                                                            boxShadow: '0 4px 12px rgba(0, 123, 255, 0.2)',
                                                        }}
                                                    >
                                                        Choose CSV File
                                                    </button>
                                                    <small className="text-muted">Supports CSV files only</small>
                                                </div>
                                                <input
                                                    ref={fileInputRef}
                                                    type="file"
                                                    accept=".csv"
                                                    onChange={handleInputChange}
                                                    style={{ display: 'none' }}
                                                />
                                            </div>
                                        )}
                                    </div>
                                </>
                            ) : (
                                <div>
                                    <div
                                        className="alert alert-success"
                                        style={{ borderRadius: '8px', padding: '1rem' }}
                                    >
                                        <h6 className="mb-3">Import Completed Successfully!</h6>
                                        <div className="row mb-3">
                                            <div className="col-12">
                                                <small>
                                                    <strong>Total Processed:</strong> {importResult.totalProcessed}
                                                </small>
                                            </div>
                                            <div className="col-12">
                                                <small>
                                                    <strong>File Name:</strong>
                                                </small>
                                                <div style={{ fontSize: '0.8rem', wordBreak: 'break-all' }}>
                                                    {importResult.fileName}
                                                </div>
                                            </div>
                                        </div>
                                        <div className="row">
                                            <div className="col-4">
                                                <div className="text-success">
                                                    <small>
                                                        <strong>Created:</strong> {importResult.created}
                                                    </small>
                                                </div>
                                            </div>
                                            <div className="col-4">
                                                <div className="text-info">
                                                    <small>
                                                        <strong>Updated:</strong> {importResult.updated}
                                                    </small>
                                                </div>
                                            </div>
                                            <div className="col-4">
                                                <div className="text-danger">
                                                    <small>
                                                        <strong>Errors:</strong> {importResult.errors.length}
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {importResult.errors.length > 0 && (
                                        <div
                                            className="alert alert-warning"
                                            style={{ borderRadius: '8px', padding: '1rem' }}
                                        >
                                            <strong>Errors encountered:</strong>
                                            <div
                                                className="import-error-list"
                                                style={{ maxHeight: '150px', overflowY: 'auto' }}
                                            >
                                                <ul className="mb-0 mt-2" style={{ fontSize: '0.9rem' }}>
                                                    {importResult.errors.slice(0, 10).map((error, index) => (
                                                        <li key={index}>{error}</li>
                                                    ))}
                                                    {importResult.errors.length > 10 && (
                                                        <li>... and {importResult.errors.length - 10} more errors</li>
                                                    )}
                                                </ul>
                                            </div>
                                        </div>
                                    )}

                                    {importResult.filePath && (
                                        <div className="text-start mt-3">
                                            <a
                                                href={importResult.filePath}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="btn btn-outline-primary btn-sm"
                                                style={{ borderRadius: '6px' }}
                                            >
                                                <i className="feather icon-download me-1"></i>
                                                Download Processed File
                                            </a>
                                        </div>
                                    )}
                                </div>
                            )}
                        </div>
                        <div className="modal-footer" style={{ padding: '1rem 1.5rem' }}>
                            <button
                                className="btn btn-secondary"
                                onClick={handleClose}
                                disabled={isLoading}
                                style={{ borderRadius: '6px' }}
                            >
                                Close
                            </button>
                            {!importResult && (
                                <button
                                    className="btn btn-primary"
                                    onClick={handleSubmit}
                                    disabled={!selectedFile || isLoading}
                                    style={{ borderRadius: '6px' }}
                                >
                                    {isLoading && (
                                        <span
                                            className="spinner-border spinner-border-sm me-2"
                                            role="status"
                                            aria-hidden="true"
                                        ></span>
                                    )}
                                    {isLoading ? 'Importing...' : 'Import CSV'}
                                </button>
                            )}
                        </div>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}
        </>
    );
}
