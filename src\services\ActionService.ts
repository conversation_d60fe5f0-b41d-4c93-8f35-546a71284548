import Action from 'types/Action';
import { gql } from 'graphql-request';
import Group from '../types/Group';
import { filter } from 'lodash';

export const ACTION_LIST = gql`
    query Actions_list($page: Int!, $limit: Int!, $search: String, $filters: [String!], $sort: String) {
        actions_list(body: { page: $page, limit: $limit, search: $search, filters: $filters, sort: $sort }) {
            data {
                id
                created_by
                updated_by
                created_at
                updated_at
                name
                url
                icon
                parent_id
                display_order
            }
        }
    }
`;

export const ACTION_CREATE = gql`
    mutation Actions_create($name: String!, $url: String, $icon: String, $parent_id: Int, $display_order: Int!) {
        actions_create(
            body: { name: $name, url: $url, icon: $icon, parent_id: $parent_id, display_order: $display_order }
        ) {
            id
            name
            url
            icon
            parent_id
            display_order
        }
    }
`;

export const ACTION_UPDATE = gql`
    mutation Actions_update(
        $id: Int!
        $name: String!
        $url: String
        $icon: String
        $parent_id: Int
        $display_order: Int!
    ) {
        actions_update(
            id: $id
            body: { name: $name, url: $url, icon: $icon, parent_id: $parent_id, display_order: $display_order }
        ) {
            id
            name
            url
            icon
            parent_id
            display_order
        }
    }
`;

export const ACTION_DELETE = gql`
    mutation Actions_delete($id: Int!) {
        actions_delete(id: $id)
    }
`;

/**
 * Lấy và hợp nhất các action từ các group của user
 * @param groups Danh sách các group của user
 * @returns Mảng các action duy nhất, bao gồm cả children được hợp nhất
 */
export const getUniqueActions = (groups: Group[]): Action[] => {
    // Sử dụng Map để lưu trữ các action duy nhất
    const actionMap = new Map<number, Action>();

    // Hàm đệ quy để hợp nhất các children
    const mergeChildren = (existingChildren: Action[] = [], newChildren: Action[] = []): Action[] => {
        const childrenMap = new Map<number, Action>();

        // Thêm các children hiện có vào map
        existingChildren.forEach((child) => {
            childrenMap.set(child.id!, { ...child, children: child.children || [] });
        });

        // Hợp nhất các children mới
        newChildren.forEach((newChild) => {
            const existingChild = childrenMap.get(newChild.id!);
            if (existingChild) {
                // Nếu child đã tồn tại, hợp nhất children của nó
                existingChild.children = mergeChildren(existingChild.children, newChild.children);
            } else {
                // Nếu child chưa tồn tại, thêm mới
                childrenMap.set(newChild.id!, { ...newChild });
            }
        });

        // Chuyển map về mảng và sắp xếp
        return Array.from(childrenMap.values()).sort((a, b) => (a.display_order || 0) - (b.display_order || 0));
    };

    // Xử lý các action từng group
    groups.forEach((group) => {
        (group.actions ?? []).forEach((action) => {
            const existingAction = actionMap.get(action.id!);

            if (existingAction) {
                // Nếu action đã tồn tại, hợp nhất children
                existingAction.children = mergeChildren(existingAction.children, action.children);
            } else {
                // Thêm action mới vào map
                actionMap.set(action.id!, { ...action });
            }
        });
    });

    // Chuyển map về mảng và sắp xếp
    return Array.from(actionMap.values()).sort((a, b) => (a.display_order || 0) - (b.display_order || 0));
};

export const getHierarchyActions = (actions: Action[]): Action[] => {
    const retVal: Action[] = [];
    actions.forEach((a1) => {
        if (!a1.parent_id) {
            a1.children = filter(actions, { parent_id: a1.id });
            retVal.push(a1);
        }
    });
    return retVal;
};

export const getFlatActions = (actions: Action[]): Action[] => {
    const retVal: Action[] = [];
    actions.forEach((a1) => {
        if (!a1.parent_id) {
            retVal.push(a1);
            actions.forEach((a2) => {
                if (a2.parent_id === a1.id) {
                    retVal.push(a2);
                }
            });
        }
    });
    return retVal;
};
