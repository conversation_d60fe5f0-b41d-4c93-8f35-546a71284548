import { FILTER_CONDITIONS } from '../constants/common';
import { baseFilterConfig, BaseModel, BaseSearch, DataList, FilterConfig } from './common';
import { ItemFile, ItemStatus } from './common/Item';

export default interface Component extends BaseModel {
    name: string;
    desc?: string;
    status_id: ItemStatus;
    file: ItemFile;
}

export interface SearchComponent extends BaseSearch {
    status_id?: string;
}

export type SearchComponentParam = {
    [key in keyof SearchComponent]: string;
};

export interface ComponentQuery {
    template_component_list: DataList<Component>;
}

export interface ComponentViewResponse {
    template_component_view: Component;
}

export const componentFilterConfig: FilterConfig = {
    ...baseFilterConfig,
    status_id: { key: 'status_id', operator: FILTER_CONDITIONS.EQUAL },
};
