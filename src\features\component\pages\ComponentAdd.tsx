import ContentHeader from 'components/partials/ContentHeader';
import { Helmet } from 'react-helmet-async';
import UpdateComponentForm from '../components/UpdateComponentForm';

export default function ComponentAdd() {
    return (
        <>
            <Helmet>
                <title>Add Elementor Component</title>
            </Helmet>
            <ContentHeader
                title="Add Elementor Component"
                breadcrumbs={[
                    {
                        text: 'Elementor Components',
                        to: '/component',
                    },
                    {
                        text: 'Add Elementor Component',
                        to: '',
                    },
                ]}
            />
            <div className="content-body">
                <div className="col-12">
                    <UpdateComponentForm id={0} />
                </div>
            </div>
        </>
    );
}
