import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import { useCallback, useEffect, useLayoutEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import * as yup from 'yup';
import UpdateButton from 'components/partials/UpdateButton';
import { toggleModalOpen, showToast, generateFilters } from 'utils/common';
import { SUCCESS_MESSAGE, QUERY_KEY } from 'constants/common';
import { useGraphQLMutation } from 'hooks/useGraphQLMutation';
import { SMTP_CREATE, SMTP_UPDATE, SMTP_DELETE, SMTP_LIST } from 'services/SmtpService';
import { SmtpConfig, smtpFilterConfig, SmtpQuery } from 'types/Smtp';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import ModalConfirm from 'components/partials/ModalConfirm';
import { Eye, EyeOff } from 'react-feather';
import isEmpty from 'lodash/isEmpty';

interface IProps {
    show: boolean;
    userId: number;
    changeShow: (s: boolean) => void;
}

export default function ModalSmtpConfig({ show, userId, changeShow }: Readonly<IProps>) {
    const { t } = useTranslation();
    const [showConfirmModal, setShowConfirmModal] = useState(false);
    const [showPassword, setShowPassword] = useState(false);

    useLayoutEffect(() => toggleModalOpen(show), [show]);

    const yupObj = {
        host: yup.string().required(t('error.required')).trim(),
        port: yup
            .number()
            .typeError(t('error.number'))
            .required(t('error.required'))
            .min(1, t('error.min_1'))
            .max(65535, t('error.max_port')),
        ssl: yup.boolean().default(false),
        username: yup.string().required(t('error.required')).trim(),
        password: yup.string().required(t('error.required')).trim(),
    };

    const schema = yup.object(yupObj).required();
    const {
        register,
        handleSubmit,
        reset,
        formState: { errors },
    } = useForm<SmtpConfig>({
        resolver: yupResolver(schema),
    });

    const { data: smtpData } = useGraphQLQuery<SmtpQuery>(
        [QUERY_KEY.SMTPS, userId, show],
        SMTP_LIST,
        {
            page: 1,
            limit: 999,
            filters: generateFilters({ customer_id: userId }, smtpFilterConfig),
        },
        '',
        {
            enabled: show && userId > 0,
        }
    );

    const smtps = smtpData?.smtps_list?.data || [];

    const resetForm = useCallback(() => {
        const smtp = smtpData?.smtps_list?.data || [];
        if (show && !isEmpty(smtp)) {
            reset({
                host: smtp[0].smtp_config.host,
                port: smtp[0].smtp_config.port,
                username: smtp[0].smtp_config.username,
                password: smtp[0].smtp_config.password,
                ssl: smtp[0].smtp_config.ssl,
            });
        } else {
            reset({
                host: '',
                port: 0,
                username: '',
                password: '',
                ssl: false,
            });
        }
    }, [show, reset, smtpData]);

    useEffect(() => {
        resetForm();
    }, [resetForm]);

    const smtpCreateMutation = useGraphQLMutation<{}, { customer_id: number; smtp_config: SmtpConfig }>(
        SMTP_CREATE,
        '',
        {
            onSuccess() {
                showToast(true, [SUCCESS_MESSAGE]);
                changeShow(false);
            },
        }
    );

    const smtpUpdateMutation = useGraphQLMutation<{}, { id: number; input: { smtp_config: Omit<SmtpConfig, 'id'> } }>(
        SMTP_UPDATE,
        '',
        {
            onSuccess() {
                showToast(true, [SUCCESS_MESSAGE]);
                changeShow(false);
            },
        }
    );

    const smtpDeleteMutation = useGraphQLMutation<{}, { id: number }>(SMTP_DELETE, '', {
        onSuccess() {
            showToast(true, ['SMTP configuration deleted successfully']);
            changeShow(false);
        },
    });

    const onSubmit = async (formData: SmtpConfig) => {
        const input = {
            customer_id: userId,
            smtp_config: {
                host: formData.host,
                port: formData.port,
                username: formData.username,
                password: formData.password,
                ssl: formData.ssl,
            },
        };

        if (!isEmpty(smtps) && smtps[0].id) {
            smtpUpdateMutation.mutate({ id: smtps[0].id, input });
        } else {
            smtpCreateMutation.mutate(input);
        }
    };

    const handleDelete = () => {
        setShowConfirmModal(true);
    };

    const submitDelete = () => {
        const smtpId = !isEmpty(smtps) ? smtps[0].id : null;
        if (smtpId) {
            smtpDeleteMutation.mutate({ id: smtpId });
        }
        setShowConfirmModal(false);
    };

    const isLoading = smtpCreateMutation.isPending || smtpUpdateMutation.isPending || smtpDeleteMutation.isPending;
    const isEdit = !isEmpty(smtps);

    return (
        <>
            <div
                className={classNames('modal fade text-start modal-primary', { show })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-dialog-centered">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">SMTP Config</h5>
                            <button
                                type="button"
                                className="btn-close"
                                onClick={() => changeShow(false)}
                                disabled={isLoading}
                            />
                        </div>
                        <form className="validate-form pt-50" onSubmit={handleSubmit(onSubmit)}>
                            <div className="modal-body">
                                <div className="row">
                                    <div className="col-12 mb-1">
                                        <label className="form-label">
                                            Host <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('host')}
                                            type="text"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.host?.message),
                                            })}
                                            placeholder="Host"
                                        />
                                        <span className="error">{errors.host?.message}</span>
                                    </div>

                                    <div className="col-12 mb-1">
                                        <label className="form-label">
                                            Port <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('port', { valueAsNumber: true })}
                                            type="number"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.port?.message),
                                            })}
                                            placeholder="0"
                                            min="1"
                                            max="65535"
                                        />
                                        <span className="error">{errors.port?.message}</span>
                                    </div>

                                    <div className="col-12 mb-1">
                                        <label className="form-label">
                                            Username <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('username')}
                                            type="text"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.username?.message),
                                            })}
                                            placeholder={t('name')}
                                        />
                                        <span className="error">{errors.username?.message}</span>
                                    </div>

                                    <div className="col-12 mb-1">
                                        <label className="form-label">
                                            Password <span className="error">*</span>
                                        </label>
                                        <div className="position-relative">
                                            <input
                                                {...register('password')}
                                                type={showPassword ? 'text' : 'password'}
                                                className={classNames('form-control pe-5', {
                                                    'is-invalid': Boolean(errors.password?.message),
                                                })}
                                                placeholder={t('password')}
                                            />
                                            <span
                                                onClick={() => setShowPassword(!showPassword)}
                                                className="position-absolute top-50 end-0 translate-middle-y me-2 cursor-pointer text-muted"
                                                style={{ zIndex: 2 }}
                                            >
                                                {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                                            </span>
                                        </div>
                                        <span className="error">{errors.password?.message}</span>
                                    </div>

                                    <div className="col-12 mb-1">
                                        <div className="form-check">
                                            <input
                                                {...register('ssl')}
                                                type="checkbox"
                                                className="form-check-input"
                                                id="smtp-ssl"
                                            />
                                            <label className="form-check-label" htmlFor="smtp-ssl">
                                                Use SSL
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="modal-footer">
                                {isEdit && (
                                    <button
                                        type="button"
                                        className="btn btn-danger me-auto"
                                        onClick={handleDelete}
                                        disabled={isLoading}
                                    >
                                        Delete
                                    </button>
                                )}
                                <button
                                    type="button"
                                    className="btn btn-outline-secondary"
                                    onClick={() => changeShow(false)}
                                    disabled={isLoading}
                                >
                                    Cancel
                                </button>
                                <UpdateButton
                                    isLoading={isLoading}
                                    btnText={isEdit ? 'Update' : 'Save'}
                                    hasDivWrap={false}
                                />
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            {show && <div className="modal-backdrop fade show" />}

            <ModalConfirm
                show={showConfirmModal}
                text="Are you sure you want to delete this SMTP configuration?"
                btnDisabled={smtpDeleteMutation.isPending}
                changeShow={setShowConfirmModal}
                submitAction={submitDelete}
            />
        </>
    );
}
