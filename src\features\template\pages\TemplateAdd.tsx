import ContentHeader from 'components/partials/ContentHeader';
import { Helmet } from 'react-helmet-async';
import UpdateTemplateForm from '../components/UpdateTemplateForm';
import { useNavigate, useParams } from 'react-router-dom';
import { useMemo } from 'react';
import { getFieldInArrayObject } from '../../../utils/common';
import { TemplateTypeNames } from '../../../types/Template';

export default function TemplateAdd() {
    const { type } = useParams();
    const navigate = useNavigate();
    const typeId = useMemo(() => +getFieldInArrayObject(TemplateTypeNames, type ?? '', 'id', '-1', 'name'), [type]);
    if (typeId === -1) {
        navigate('/not-found');
    }

    return (
        <>
            <Helmet>
                <title>Add Template</title>
            </Helmet>
            <ContentHeader
                title="Add Template"
                breadcrumbs={[
                    {
                        text: 'Templates',
                        to: `/template/list/${type}`,
                    },
                    {
                        text: 'Add Template',
                        to: '',
                    },
                ]}
            />
            <div className="content-body">
                <div className="col-12">
                    <UpdateTemplateForm id={0} type={type} />
                </div>
            </div>
        </>
    );
}
