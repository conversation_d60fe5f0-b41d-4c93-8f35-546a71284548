import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import UpdateButton from 'components/partials/UpdateButton';
import FormatNumber from 'components/partials/FormatNumber';
import { SUCCESS_MESSAGE } from 'constants/common';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Link, useNavigate } from 'react-router-dom';
import { ItemStatus, ItemStatusNames } from 'types/common/Item';
import { selectItem, showToast } from 'utils/common';
import * as yup from 'yup';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import omitBy from 'lodash/omitBy';
import omit from 'lodash/omit';
import isNull from 'lodash/isNull';
import {
    FeatureList,
    ItemPlanType,
    ItemPlanTypeNames,
    SubscriptionPlan,
    SubscriptionPlanFormData,
} from 'types/SubcriptionPlan';
import { SUBSCRIPTION_PLAN_CREATE, SUBSCRIPTION_PLAN_UPDATE } from 'services/SubscriptionPlanService';
import FeatureItem from './FeatureItem';
import './SubscriptionPlanForm.css';

interface IProps {
    id: string;
    subscriptionPlan?: SubscriptionPlan;
    isProfile?: boolean;
}

export default function UpdateSubscriptionPlanForm({ id, subscriptionPlan }: Readonly<IProps>) {
    const navigate = useNavigate();
    const { t } = useTranslation();

    const yupObj = {
        name: yup.string().required(t('error.required')).max(255).trim(),
        price: yup
            .number()
            .typeError(t('error.number'))
            .required(t('error.required'))
            .min(0, t('error.min_0'))
            .transform((value) => (isNaN(value) ? undefined : Number(value))), // Cho phép số thập phân
        display_order: yup
            .number()
            .typeError(t('error.number'))
            .required(t('error.required'))
            .min(1, t('error.min_1'))
            .max(99, t('error.max_99')),
        trial_days: yup.number().when('type_id', {
            is: ItemPlanType.TRIAL,
            then: (schema) =>
                schema
                    .typeError(t('error.number'))
                    .required(t('error.required'))
                    .min(0, t('error.min_0'))
                    .max(30, t('error.max_30')),
            otherwise: (schema) => schema.notRequired().nullable(),
        }),
        features: yup.array().of(
            yup.object().shape({
                label: yup.string().required(),
                type: yup.string().required(), // Thêm type vào schema
                value: yup.mixed().when('type', {
                    is: 'number',
                    then: () =>
                        yup
                            .number()
                            .transform((val: string | number) => (val === '' ? undefined : Number(val)))
                            .typeError(t('error.number'))
                            .required(t('error.required'))
                            .min(0, t('error.min_0')),
                    otherwise: (schema) =>
                        schema.when('type', {
                            is: 'checkbox',
                            then: () => yup.boolean(),
                            otherwise: (schema) =>
                                schema.when('type', {
                                    is: 'dropdown',
                                    then: () =>
                                        yup.string().required(t('error.required')).notOneOf([''], t('error.required')),
                                    otherwise: (schema) => schema,
                                }),
                        }),
                }),
            })
        ),
    };

    const schema = yup.object(yupObj).required();
    const {
        register,
        handleSubmit,
        setValue,
        control,
        reset,
        watch,
        formState: { isSubmitting, errors },
    } = useForm<SubscriptionPlanFormData>({
        resolver: yupResolver(schema),
        defaultValues: {
            status_id: ItemStatus.ACTIVE,
            type_id: ItemPlanType.TRIAL,
            trial_days: 0,
            features: FeatureList.map((feature) => ({
                label: feature.label,
                type: feature.type,
                value:
                    feature.type === 'number'
                        ? 0
                        : feature.type === 'dropdown' && feature.options && feature.options.length > 0
                        ? feature.options[0]
                        : feature.type === 'checkbox'
                        ? false
                        : undefined,
            })),
        },
    });

    const planType = watch('type_id');

    useEffect(() => {
        if (id && subscriptionPlan) {
            const formFeatures = FeatureList.map((feature, idx) => {
                const dataFeature = subscriptionPlan.features?.find((s) => s.id === idx + 1);
                let convertedValue;
                if (dataFeature) {
                    if (feature.type === 'number') {
                        convertedValue = dataFeature.value ? Number(dataFeature.value) : 0;
                    } else if (feature.type === 'checkbox') {
                        convertedValue = dataFeature.value === 'true';
                    } else {
                        convertedValue =
                            dataFeature.value ||
                            (feature.type === 'dropdown' && feature.options && feature.options.length > 0
                                ? feature.options[0]
                                : undefined);
                    }
                } else {
                    convertedValue =
                        feature.type === 'number'
                            ? 0
                            : feature.type === 'dropdown' && feature.options && feature.options.length > 0
                            ? feature.options[0]
                            : feature.type === 'checkbox'
                            ? false
                            : undefined;
                }
                return {
                    label: feature.label,
                    type: feature.type,
                    value: convertedValue,
                };
            });
            reset({
                ...subscriptionPlan,
                features: formFeatures,
            });
        } else {
            setValue('type_id', ItemPlanType.TRIAL);
            setValue('status_id', ItemStatus.ACTIVE);
        }
    }, [reset, subscriptionPlan, id, setValue]);

    const subscriptionsPlanCreateMutation = useGraphQLMutation<{}, { body: Partial<SubscriptionPlan> }>(
        SUBSCRIPTION_PLAN_CREATE,
        '',
        {
            onSuccess() {
                showToast(true, [SUCCESS_MESSAGE]);
                setTimeout(() => {
                    navigate('/subscriptionPlan');
                }, 2000);
            },
        }
    );

    const userUpdateMutation = useGraphQLMutation<{}, { id: number; body: Partial<SubscriptionPlan> }>(
        SUBSCRIPTION_PLAN_UPDATE,
        '',
        {
            onSuccess() {
                showToast(true, [SUCCESS_MESSAGE]);
                setTimeout(() => {
                    navigate('/subscriptionPlan');
                }, 2000);
            },
        }
    );

    const onSubmit = async (formData: SubscriptionPlanFormData) => {
        delete formData.id;
        const repareFeatures = formData.features?.map((item, index) => ({
            id: FeatureList.findIndex((f) => f.label === item.label) + 1,
            value: String(item.value),
        }));
        const submitData: SubscriptionPlan = {
            ...formData,
            status_id: +formData.status_id,
            type_id: +formData.type_id,
            features: repareFeatures,
        };
        if (!repareFeatures?.length) {
            delete submitData.features;
        }
        const data = omit(submitData, ['created_at', 'created_by', 'updated_at', 'updated_by']);
        const dataUpdate = omitBy(data, isNull);
        if (!id) {
            subscriptionsPlanCreateMutation.mutate({ body: dataUpdate });
        } else {
            userUpdateMutation.mutate({ id: Number(id), body: dataUpdate });
        }
    };

    return (
        <div className="card">
            <div className="card-body py-2 my-25">
                <form className="validate-form pt-50" onSubmit={handleSubmit(onSubmit)}>
                    <h4 className="card-title">{t('baseInformation')}</h4>
                    <div className="row">
                        <div className="col-12 col-sm-4 mb-1">
                            <label className="form-label">
                                {t('name')}
                                <span className="error">*</span>
                            </label>
                            <input
                                {...register('name')}
                                type="text"
                                className={classNames('form-control', {
                                    'is-invalid': Boolean(errors.name?.message),
                                })}
                                readOnly={true}
                            />
                            <span className="error">{errors.name?.message}</span>
                        </div>
                        <div className="col-12 col-sm-4 mb-1">
                            <label className="form-label">
                                {t('price')}
                                <span className="error">*</span>
                            </label>
                            <FormatNumber
                                value={watch('price')}
                                isInput={true}
                                inputClass={classNames('', {
                                    'is-invalid': Boolean(errors.price?.message),
                                })}
                                onValueChange={(value: number) => setValue('price', value)}
                                inputDisabled={true}
                            />
                            <span className="error">{errors.price?.message}</span>
                        </div>

                        <div className="col-12 col-sm-4 mb-1">
                            <label className="form-label">{t('statusName')}</label>
                            <select
                                {...register('status_id', { valueAsNumber: true })}
                                className="form-select"
                                disabled={true}
                            >
                                {selectItem(ItemStatusNames, t, true)}
                            </select>
                        </div>
                        <div className="col-12 col-sm-4 mb-1">
                            <label className="form-label">
                                {t('displayOrder')} <span className="error">*</span>
                            </label>
                            <input
                                {...register('display_order')}
                                type="number"
                                className={classNames('form-control', {
                                    'is-invalid': Boolean(errors.display_order?.message),
                                })}
                                readOnly={true}
                            />
                            <span className="error">{errors.display_order?.message}</span>
                        </div>
                        <div className="col-12 col-sm-4 mb-1">
                            <label className="form-label">
                                {t('subscription_plan.plan_type')}
                                <span className="error">*</span>
                            </label>
                            <select
                                {...register('type_id', { valueAsNumber: true })}
                                className="form-control"
                                disabled={true}
                            >
                                {selectItem(ItemPlanTypeNames, t, true)}
                            </select>
                        </div>
                        {+planType === ItemPlanType.TRIAL && (
                            <div className="col-12 col-sm-4 mb-1">
                                <label className="form-label">
                                    {t('subscription_plan.trial_days')}
                                    <span className="error">*</span>
                                </label>
                                <input
                                    {...register('trial_days')}
                                    type="number"
                                    className={classNames('form-control', {
                                        'is-invalid': Boolean(errors.trial_days?.message),
                                    })}
                                    readOnly={true}
                                />
                                <span className="error">{errors.trial_days?.message}</span>
                            </div>
                        )}
                        <div className="col-12 col-sm-12 mb-1">
                            <label className="form-label">{t('subscription_plan.description')}</label>
                            <input
                                {...register('desc')}
                                type="text"
                                className={classNames('form-control')}
                                readOnly={true}
                            />
                        </div>

                        <div className="mt-2">
                            <h4 className="card-title mb-2">{t('subscription_plan.features')}</h4>
                            <div className="feature-list">
                                {FeatureList.map((feature, index) => (
                                    <FeatureItem
                                        key={index}
                                        feature={feature}
                                        index={index}
                                        control={control}
                                        errors={errors}
                                    />
                                ))}
                            </div>
                        </div>

                        <div className="col-12">
                            <div className="float-end">
                                <Link
                                    className="btn btn-outline-primary waves-effect waves-float waves-light mt-1 ms-2"
                                    to={'/subscriptionPlan'}
                                >
                                    Cancel
                                </Link>
                            </div>
                            <UpdateButton btnText={t('update')} isLoading={isSubmitting} btnClass={['mt-1']} />
                        </div>
                    </div>
                </form>
            </div>
        </div>
    );
}
