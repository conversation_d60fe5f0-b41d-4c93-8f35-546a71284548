import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import UpdateButton from 'components/partials/UpdateButton';
import { useEffect, useLayoutEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import Action from 'types/Action';
import { toggleModalOpen } from 'utils/common';
import * as yup from 'yup';

interface IProps {
    show: boolean;
    action: Action | undefined;
    listActions: Action[];
    isLoading?: boolean;
    changeShow: (s: boolean) => void;
    submitAction: (data: Action) => void;
}

export default function ModalActionUpdate({
    show,
    action,
    listActions,
    isLoading,
    changeShow,
    submitAction,
}: Readonly<IProps>) {
    const { t } = useTranslation();
    useLayoutEffect(() => toggleModalOpen(show), [show]);
    const schema = yup
        .object({
            name: yup.string().required(t('error.required')).trim(),
            display_order: yup
                .number()
                .typeError(t('error.number'))
                .required(t('error.required'))
                .min(1, t('error.min_1'))
                .max(99, t('error.max_99')),
        })
        .required();
    const {
        register,
        handleSubmit,
        reset,
        formState: { errors },
    } = useForm<Action>({
        resolver: yupResolver(schema),
    });

    useEffect(() => {
        if (action && show) {
            reset(action);
        } else {
            reset({
                name: '',
                url: '',
                parent_id: 0,
                display_order: 1,
                icon: '',
            });
        }
    }, [action, show, reset]);

    return (
        <>
            <div
                className={classNames('modal fade text-start modal-primary', { show })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-dialog-centered modal-lg">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">{t('edit')}</h5>
                            <button type="button" className="btn-close" onClick={() => changeShow(false)} />
                        </div>
                        <form className="validate-form pt-50" onSubmit={handleSubmit(submitAction)}>
                            <div className="modal-body">
                                <div className="row">
                                    <div className="col-12 col-sm-6 mb-1">
                                        <label className="form-label">
                                            {t('actionName')} <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('name')}
                                            type="text"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.name?.message),
                                            })}
                                        />
                                        <span className="error">{errors.name?.message}</span>
                                    </div>
                                    <div className="col-12 col-sm-6 mb-1">
                                        <label className="form-label">{t('url')}</label>
                                        <input {...register('url')} type="text" className="form-control" />
                                    </div>
                                    <div className="col-12 col-sm-6 mb-1">
                                        <label className="form-label">{t('parent')}</label>
                                        <select
                                            {...register('parent_id', { valueAsNumber: true })}
                                            className="form-select"
                                        >
                                            <option value={''}>--</option>
                                            {listActions
                                                .filter((la) => !la.parent_id)
                                                .map((la) => (
                                                    <option key={la.id} value={la.id}>
                                                        {la.name}
                                                    </option>
                                                ))}
                                        </select>
                                    </div>
                                    <div className="col-12 col-sm-6 mb-1">
                                        <label className="form-label">
                                            {t('displayOrder')} <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('display_order')}
                                            type="number"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.display_order?.message),
                                            })}
                                        />
                                        <span className="error">{errors.display_order?.message}</span>
                                    </div>
                                    <div className="col-12 col-sm-6 mb-1">
                                        <label className="form-label">{t('icon')}</label>
                                        <input {...register('icon')} type="text" className="form-control" />
                                    </div>
                                </div>
                            </div>
                            <div className="modal-footer">
                                <UpdateButton btnText={t('update')} isLoading={isLoading} hasDivWrap={false} />
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}
        </>
    );
}
