import SearchForm from 'components/partials/SearchForm';
import { useTranslation } from 'react-i18next';
import { SearchField } from 'types/common/Search';
import { convertConstantToSelectOptions, convertSelectToStringKey } from 'utils/common';
import { DashboardStatisticTypeNames } from 'types/Dashboard';

interface IProps {
    isLoading: boolean;
    defaultOpen?: boolean;
}

export default function SearchDashboardForm({ isLoading, defaultOpen = false }: Readonly<IProps>) {
    const { t } = useTranslation();

    const fields: SearchField[] = [
        {
            name: 'type_id',
            type: 'select',
            label: 'Chart By',
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: false,
                choices: convertSelectToStringKey(convertConstantToSelectOptions(DashboardStatisticTypeNames, t, true)),
            },
        },
        {
            name: 'start_date',
            type: 'date',
            label: 'From Date',
            wrapClassName: 'col-md-4 col-12',
        },
        {
            name: 'end_date',
            type: 'date',
            label: 'End Date',
            wrapClassName: 'col-md-4 col-12',
        },
    ];

    return <SearchForm fields={fields} isLoading={isLoading} defaultOpen={defaultOpen} />;
}
