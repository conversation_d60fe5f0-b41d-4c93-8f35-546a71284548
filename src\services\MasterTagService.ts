import { gql } from 'graphql-request';

export const MASTER_TAG_LIST = gql`
    query Master_tags_list($page: Int!, $limit: Int!, $filters: [String!], $sort: String, $search: String) {
        master_tags_list(body: { page: $page, limit: $limit, filters: $filters, sort: $sort, search: $search }) {
            totalCount
            totalPages
            currentPage
            data {
                id
                name
                type
                display_name
                tool_tip
            }
        }
    }
`;

export const MASTER_TAG_CREATE = gql`
    mutation Master_tags_create($body: MasterTagSaveInputDto!) {
        master_tags_create(body: $body) {
            id
        }
    }
`;

export const MASTER_TAG_UPDATE = gql`
    mutation Master_tags_update($id: Int!, $body: MasterTagSaveInputDto!) {
        master_tags_update(id: $id, body: $body) {
            id
        }
    }
`;

export const MASTER_TAG_DELETE = gql`
    mutation Master_tags_delete($id: Int!) {
        master_tags_delete(id: $id)
    }
`;

export const MASTER_TAG_IMPORT = '/master-tags/import';
