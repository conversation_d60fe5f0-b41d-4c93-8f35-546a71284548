import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import UpdateButton from 'components/partials/UpdateButton';
import { useEffect, useLayoutEffect } from 'react';
import { useForm } from 'react-hook-form';
import WebsiteStyle from 'types/WebsiteStyle';
import { ItemStatus, ItemStatusNames2 } from 'types/common/Item';
import { selectItem, toggleModalOpen } from 'utils/common';
import * as yup from 'yup';
import { useTranslation } from 'react-i18next';

interface IProps {
    show: boolean;
    websiteStyle?: WebsiteStyle;
    isLoading?: boolean;
    changeShow: (s: boolean) => void;
    submitAction: (data: WebsiteStyle) => void;
}

export default function ModalWebsiteStyleUpdate({
    show,
    websiteStyle,
    isLoading,
    changeShow,
    submitAction,
}: Readonly<IProps>) {
    const { t } = useTranslation();
    useLayoutEffect(() => toggleModalOpen(show), [show]);

    const schema = yup
        .object({
            name: yup.string().required(t('error.required')).trim(),
            display_order: yup
                .number()
                .typeError(t('error.number'))
                .required(t('error.required'))
                .min(1, t('error.min_1'))
                .max(99, t('error.max_99')),
        })
        .required();

    const {
        register,
        handleSubmit,
        reset,
        formState: { errors },
    } = useForm<WebsiteStyle>({
        resolver: yupResolver(schema),
    });

    useEffect(() => {
        if (websiteStyle && show) {
            reset(websiteStyle);
        } else {
            reset({
                name: '',
                desc: '',
                display_order: 1,
                status_id: ItemStatus.ACTIVE,
            });
        }
    }, [websiteStyle, show, reset]);

    return (
        <>
            <div
                className={classNames('modal fade text-start modal-primary', { show })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-dialog-centered">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">{websiteStyle ? 'Edit Website Style' : 'Add Website Style'}</h5>
                            <button type="button" className="btn-close" onClick={() => changeShow(false)} />
                        </div>
                        <form className="validate-form pt-50" onSubmit={handleSubmit(submitAction)}>
                            <div className="modal-body">
                                <div className="row">
                                    <div className="col-12 mb-1">
                                        <label className="form-label">
                                            Name <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('name')}
                                            type="text"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.name?.message),
                                            })}
                                        />
                                        <span className="error">{errors.name?.message}</span>
                                    </div>
                                    <div className="col-12 mb-1">
                                        <label className="form-label">Description</label>
                                        <textarea {...register('desc')} className="form-control" rows={2} />
                                    </div>
                                    <div className="col-12 mb-1">
                                        <label className="form-label">
                                            Display Order <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('display_order')}
                                            type="number"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.display_order?.message),
                                            })}
                                        />
                                        <span className="error">{errors.display_order?.message}</span>
                                    </div>
                                    <div className="col-12 mb-1">
                                        <label className="form-label">Status</label>
                                        <select
                                            {...register('status_id', { valueAsNumber: true })}
                                            className="form-select"
                                        >
                                            {selectItem(ItemStatusNames2, t, true)}
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div className="modal-footer">
                                <UpdateButton btnText={t('update')} isLoading={isLoading} hasDivWrap={false} />
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}
        </>
    );
}
