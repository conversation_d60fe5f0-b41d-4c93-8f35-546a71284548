import { <PERSON>Model, BaseSearch, DataList } from './common';
import { ItemParam } from './common/Item';

export interface ChartDataItem extends BaseModel {
    label: string;
    paid: number;
    trial: number;
}

export interface DashboardStatisticResponse {
    transactions_statistics: ChartDataItem[];
}

export interface SearchDashboard extends BaseSearch {
    start_date?: string;
    end_date?: string;
    type_id?: string;
}

export type SearchDashboardParam = {
    [key in keyof SearchDashboard]: string;
};

export interface DashboardTodayStatistics {
    total_transactions: number;
    trial_transactions: number;
}

export interface DashboardTodayStatisticsResponse {
    transactions_today_statistics: DashboardTodayStatistics;
}

export interface DashboardByCode {
    code: string;
    name: string;
    value: string;
}

export interface DashboardByCodeResponse {
    configs_by_code: DashboardByCode;
}
export enum DashboardStatisticType {
    DAILY = 1,
    MONTHLY = 2,
}

export const DashboardStatisticTypeNames: ItemParam[] = [
    { id: DashboardStatisticType.DAILY, name: 'day', className: 'badge badge-glow bg-info' },
    { id: DashboardStatisticType.MONTHLY, name: 'month', className: 'badge badge-glow bg-warning' },
];
