import { useState } from 'react';
import { Link } from 'react-router-dom';
import Tooltip from '@mui/material/Tooltip';
import { Globe, LogIn, Power, Wifi, RotateCcw, List, FileText } from 'react-feather';
import ModalBackupList from './ModalBackup';
import { genTableIndex, showToast } from 'utils/common';
import { useGraphQLMutation } from 'hooks/useGraphQLMutation';
import { UPDATE_WEBSITE_STATUS, WEBSITE_HEALTH_CHECK } from 'services/WebsiteService';
import { SUCCESS_MESSAGE, W_TOKEN_KEY } from 'constants/common';
import Website, { WebsiteHealthCheck, WebsiteStatus } from 'types/Website';
import { Paging } from 'types/common';
import ModalDNSRecords from './DsnWebsite';
import ModalInstallPlugins from './PluginsModal';
import ModalLogViewer from './ModalLogViewer';

interface IProps {
    items: Website[];
    paging: Paging;
    refetch: () => void;
}

export default function ListWebsite({ items, paging, refetch }: IProps) {
    const [modalDnsOpen, setModalDnsOpen] = useState(false);
    const [backupModalOpen, setBackupModalOpen] = useState(false);
    const [currentWebsiteId, setCurrentWebsiteId] = useState<number>();
    const [selectedDomain, setSelectedDomain] = useState<string>();
    const [installModalOpen, setInstallModalOpen] = useState(false);
    const [installDomain, setInstallDomain] = useState<string>();
    const [logModalOpen, setLogModalOpen] = useState(false);
    const [logDomain, setLogDomain] = useState<string>();
    const [isLoading, setIsLoading] = useState(false);

    const updateMutation = useGraphQLMutation(UPDATE_WEBSITE_STATUS, 'Websites_update_status', {
        onSuccess: () => {
            showToast(true, [SUCCESS_MESSAGE]);
            refetch();
        },
    });

    const healthCheckMutation = useGraphQLMutation<WebsiteHealthCheck, { id: number }>(
        WEBSITE_HEALTH_CHECK,
        'Websites_health_check',
        {
            onSuccess: (data) => {
                if (data.websites_health_check.success) {
                    showToast(true, [`${data.websites_health_check.domain} is live`]);
                } else {
                    showToast(false, [`${data.websites_health_check.domain} is disabled or inactive`]);
                }
            },
        }
    );

    const toggleStatus = (item: Website) => {
        const newStatus = item.status_id === WebsiteStatus.ACTIVE ? WebsiteStatus.DISABLED : WebsiteStatus.ACTIVE;

        updateMutation.mutate({
            id: item.id,
            input: {
                status_id: newStatus,
            },
        });
    };

    const openDnsModal = (customDomain: string) => {
        setSelectedDomain(customDomain);
        setModalDnsOpen(true);
    };

    const openBackupModal = (websiteId: number) => {
        setCurrentWebsiteId(websiteId);
        setBackupModalOpen(true);
    };

    const openInstallModal = (domain: string) => {
        setInstallDomain(domain);
        setInstallModalOpen(true);
    };

    const onCheckWebsiteHealth = (id: number) => {
        healthCheckMutation.mutate({id: id})
    };

    return (
        <>
            <div className="table-responsive">
                <table className="table">
                    <thead>
                        <tr>
                            <th className="text-center w-[30px]">No</th>
                            <th>Domain</th>
                            <th className="w-[130px]">Custom Domain</th>
                            <th>Customer</th>
                            <th className="w-[100px]">Template</th>
                            <th className="text-center" style={{ width: 240 }}>
                                Actions
                            </th>
                            <th className="text-center w-[60px]">Status</th>
                            <th className="text-center w-[1%] whitespace-nowrap px-1">Backup</th>
                            <th className="text-center w-[1%] whitespace-nowrap px-1">Plugins</th>
                            <th className="text-center w-[1%] whitespace-nowrap px-1">DNS</th>
                            <th className="text-center w-[1%] whitespace-nowrap px-1">Logs</th>
                        </tr>
                    </thead>
                    <tbody>
                        {items.map((item, index) => (
                            <tr key={item.id}>
                                <td className="text-center">
                                    {genTableIndex(index, paging.limit, paging.current_page)}
                                </td>
                                <td>
                                    <a href={`https://${item.domain}`} target="_blank" rel="noreferrer">
                                        {item.domain}
                                    </a>
                                </td>
                                <td>
                                    {item.custom_domain && (
                                        <a href={`https://${item.custom_domain}`} target="_blank" rel="noreferrer">
                                            {item.custom_domain}
                                        </a>
                                    )}
                                </td>
                                <td>
                                    <Link to={`/user/edit/${item.customer?.id}`}>{item.customer?.full_name}</Link>
                                </td>
                                <td>
                                    <Link to={`/template/edit/${item.template?.id}`}>{item.template?.name}</Link>
                                </td>
                                <td
                                    className="text-center whitespace-nowrap"
                                    style={{ width: 'max-content', whiteSpace: 'nowrap' }}
                                >
                                    <div className="flex justify-center whitespace-nowrap">
                                        <Tooltip title="Ping item">
                                            <button
                                                onClick={() => onCheckWebsiteHealth(item.id!)}
                                                className="btn bg-green-100 hover:bg-green-200 text-green-700 w-[20px] h-[20px] flex items-center justify-center"
                                                style={{ padding: '8px' }}
                                            >
                                                <Wifi size={14} />
                                            </button>
                                        </Tooltip>
                                        <Tooltip title="Direct Admin Login">
                                            <a
                                                href={`https://${item.domain}/?bypass_token=${W_TOKEN_KEY}&action=admin`}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="btn bg-orange-100 hover:bg-orange-200 text-orange-700 w-[20px] h-[20px] flex items-center justify-center"
                                                style={{ padding: '8px' }}
                                            >
                                                <LogIn size={14} />
                                            </a>
                                        </Tooltip>
                                    </div>
                                </td>

                                <td className="text-center">
                                    <button
                                        title={
                                            item.status_id === WebsiteStatus.ACTIVE
                                                ? 'Disable Website'
                                                : 'Activate Website'
                                        }
                                        className="btn btn-link p-0"
                                        style={{ padding: '8px' }}
                                        onClick={() => toggleStatus(item)}
                                    >
                                        <Power
                                            size={14}
                                            className={
                                                item.status_id === WebsiteStatus.ACTIVE ? 'text-success' : 'text-danger'
                                            }
                                        />
                                    </button>
                                </td>
                                <td className="text-center">
                                    {item.status_id === WebsiteStatus.ACTIVE && (
                                        <Tooltip title="View Backup History">
                                            <button
                                                onClick={() => openBackupModal(item.id!)}
                                                className="btn bg-gray-100 hover:bg-gray-200 text-gray-700"
                                                style={{ padding: '8px' }}
                                            >
                                                <RotateCcw size={14} />
                                            </button>
                                        </Tooltip>
                                    )}
                                </td>

                                <td>
                                    <Tooltip title="Install Plugins">
                                        <button
                                            onClick={() => openInstallModal(item.domain)}
                                            className="btn bg-purple-100 hover:bg-purple-200 text-purple-700 w-[20px] h-[20px] flex items-center justify-center"
                                            style={{ padding: '8px' }}
                                        >
                                            <List size={14} />
                                        </button>
                                    </Tooltip>
                                </td>

                                <td className="text-center">
                                    {item.custom_domain && (
                                        <Tooltip title={`Setup DNS for ${item.custom_domain}`}>
                                            <button
                                                onClick={() => openDnsModal(item.custom_domain!)}
                                                className="btn bg-blue-100 hover:bg-blue-200 text-blue-700 w-[20px] h-[20px] flex items-center justify-center"
                                                style={{ padding: '8px' }}
                                            >
                                                <Globe size={14} />
                                            </button>
                                        </Tooltip>
                                    )}
                                </td>
                                <td className="text-center">
                                    <Tooltip title="View Logs">
                                        <button
                                            onClick={() => {
                                                setLogDomain(item.domain);
                                                setLogModalOpen(true);
                                            }}
                                            className="btn bg-gray-100 hover:bg-gray-200 text-gray-700 w-[20px] h-[20px] flex items-center justify-center"
                                            style={{ padding: '8px' }}
                                        >
                                            <FileText size={14} />
                                        </button>
                                    </Tooltip>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>

            {modalDnsOpen && selectedDomain && (
                <ModalDNSRecords domain={selectedDomain} show={modalDnsOpen} onClose={() => setModalDnsOpen(false)} />
            )}

            {backupModalOpen && currentWebsiteId !== undefined && (
                <ModalBackupList
                    show={backupModalOpen}
                    onClose={() => setBackupModalOpen(false)}
                    websiteId={currentWebsiteId}
                />
            )}
            {installModalOpen && installDomain && (
                <ModalInstallPlugins
                    show={installModalOpen}
                    domain={installDomain}
                    onClose={() => setInstallModalOpen(false)}
                />
            )}
            {logModalOpen && logDomain && (
                <ModalLogViewer domain={logDomain} show={logModalOpen} changeShow={setLogModalOpen} />
            )}
        </>
    );
}
