import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import UpdateButton from 'components/partials/UpdateButton';
import { OPERATION_NAME, SUCCESS_MESSAGE } from 'constants/common';
import { useGraphQLMutation } from 'hooks/useGraphQLMutation';
import { useEffect, useState } from 'react';
import { Eye, EyeOff } from 'react-feather';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { USER_CHANGE_PASSWORD } from 'services/UserService';
import { ChangePasswordToken, UserRestPass } from 'types/User';
import { showToast } from 'utils/common';
import * as yup from 'yup';

export default function ChangePassForm() {
    const { t } = useTranslation();
    const [showPass, setShowPass] = useState(false);
    const [showRePass, setShowRePass] = useState(false);
    const [searchParams] = useSearchParams();
    const token = searchParams.get('token') ?? '';
    const navigate = useNavigate();
    const schema = yup
        .object({
            otp: yup.string().required(t('error.required')).trim(),
            password: yup
                .string()
                .required(t('error.required'))
                .trim()
                .min(12, t('error.passwordLength'))
                .matches(/[A-Z]/, t('error.passwordUppercase'))
                .matches(/[a-z]/, t('error.passwordLowercase'))
                .matches(/[0-9]/, t('error.passwordNumber'))
                .matches(/[^A-Za-z0-9]/, t('error.passwordSpecial')),
            confirm_password: yup
                .string()
                .required(t('error.required'))
                .trim()
                .oneOf([yup.ref('password'), null], t('error.passwordNotSame')),
        })
        .required();
    const {
        register,
        handleSubmit,
        formState: { isSubmitting, errors },
    } = useForm<ChangePasswordToken>({
        resolver: yupResolver(schema),
    });

    useEffect(() => {
        if (!token) {
            navigate('/not-found');
        }
    }, [navigate, token]);

    const { mutate } = useGraphQLMutation<UserRestPass, { body: ChangePasswordToken }>(USER_CHANGE_PASSWORD, '', {
        onSuccess: () => {
            showToast(true, [SUCCESS_MESSAGE]);
            navigate('/');
        },
    });

    const onSubmit = async (data: ChangePasswordToken) => {
        data.token = token;
        mutate({ body: data });
    };

    return (
        <form className="auth-login-form mt-2" onSubmit={handleSubmit(onSubmit)}>
            <div className="mb-1">
                <div className="d-flex justify-content-between">
                    <label className="form-label">{t('otp.single')}</label>
                </div>
                <div className="input-group input-group-merge form-password-toggle">
                    <input
                        {...register('otp')}
                        className={classNames('form-control', 'form-control-merge', {
                            error: Boolean(errors.otp?.message),
                        })}
                    />
                </div>
                <span className="error">{errors.otp?.message}</span>
            </div>
            <div className="mb-1">
                <div className="d-flex justify-content-between">
                    <label className="form-label">{t('newPassword')}</label>
                </div>
                <div className="input-group input-group-merge form-password-toggle">
                    <input
                        {...register('password')}
                        className={classNames('form-control', 'form-control-merge', {
                            error: Boolean(errors.password?.message),
                        })}
                        type={showPass ? 'text' : 'password'}
                        placeholder="············"
                    />
                    <span
                        className="input-group-text cursor-pointer"
                        onClick={() => setShowPass((prevShowPass) => !prevShowPass)}
                        style={{
                            borderColor: errors.password?.message ? '#ea5455' : '#d8d6de',
                        }}
                    >
                        {showPass ? <EyeOff size={14} /> : <Eye size={14} />}
                    </span>
                </div>
                <span className="error">{errors.password?.message}</span>
            </div>
            <div className="mb-1">
                <div className="d-flex justify-content-between">
                    <label className="form-label">{t('rePassword')}</label>
                </div>
                <div className="input-group input-group-merge form-password-toggle">
                    <input
                        {...register('confirm_password')}
                        className={classNames('form-control', 'form-control-merge', {
                            error: Boolean(errors.confirm_password?.message),
                        })}
                        type={showRePass ? 'text' : 'password'}
                        placeholder="············"
                    />
                    <span
                        className="input-group-text cursor-pointer"
                        onClick={() => setShowRePass((prevShowRePass) => !prevShowRePass)}
                        style={{
                            borderColor: errors.confirm_password?.message ? '#ea5455' : '#d8d6de',
                        }}
                    >
                        {showRePass ? <EyeOff size={14} /> : <Eye size={14} />}
                    </span>
                </div>
                <span className="error">{errors.confirm_password?.message}</span>
            </div>
            <UpdateButton
                isLoading={isSubmitting}
                btnClass={['w-100']}
                btnText={t('resetPassword')}
                hasDivWrap={false}
            />
        </form>
    );
}
