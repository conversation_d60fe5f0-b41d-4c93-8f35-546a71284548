import { useMemo } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import Chart from 'react-apexcharts';
import ContentHeader from 'components/partials/ContentHeader';
import Spinner from 'components/partials/Spinner';
import { ApexOptions } from 'apexcharts';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import { BAR_COLORS, FONT_STYLE, QUERY_KEY } from 'constants/common';
import { CONFIGS_BY_CODE, TRANSACTION_STATISTICS, TRANSACTIONS_TODAY_STATISTICS } from 'services/TransactionService';
import useQueryParams from 'hooks/useQueryParams';
import { format, subDays } from 'date-fns';
import isUndefined from 'lodash/isUndefined';
import omitBy from 'lodash/omitBy';
import SearchDashboardForm from '../components/SearchDashboardForm';
import {
    ChartDataItem,
    SearchDashboardParam,
    DashboardStatisticResponse,
    DashboardTodayStatisticsResponse,
    DashboardByCodeResponse,
} from 'types/Dashboard';

export default function Dashboard() {
    const { t } = useTranslation();
    const { queryParams } = useQueryParams<SearchDashboardParam>();

    const start_date = queryParams.start_date || format(subDays(new Date(), 30), 'yyyy-MM-dd');
    const end_date = queryParams.end_date;
    const type_id = queryParams.type_id || '1';

    const paramConfig: SearchDashboardParam = omitBy({ start_date, end_date, type_id }, isUndefined);

    const { data, isLoading, isRefetching } = useGraphQLQuery<DashboardStatisticResponse>(
        [QUERY_KEY.TRANSACTION_STATISTICS, paramConfig],
        TRANSACTION_STATISTICS,
        {
            start_date: paramConfig.start_date,
            end_date: paramConfig.end_date,
            type_id: Number(paramConfig.type_id),
        }
    );

    const chartData: ChartDataItem[] = useMemo(
        () => (data?.transactions_statistics ? data.transactions_statistics : []),
        [data]
    ) as ChartDataItem[];

    const { data: todayData } = useGraphQLQuery<DashboardTodayStatisticsResponse>(
        [QUERY_KEY.TRANSACTIONS_TODAY_STATISTICS],
        TRANSACTIONS_TODAY_STATISTICS
    );

    const { data: configThresholdResponse } = useGraphQLQuery<DashboardByCodeResponse>(
        [QUERY_KEY.CONFIGS_BY_CODE, 'wordpress_instance_threshold'],
        CONFIGS_BY_CODE,
        {
            code: 'wordpress_instance_threshold',
        }
    );

    const thresholdValue = configThresholdResponse?.configs_by_code.value;
    const total = todayData?.transactions_today_statistics.total_transactions || 0;
    const trial = todayData?.transactions_today_statistics.trial_transactions || 0;
    const percentTrial = total > 0 ? (trial / total) * 100 : 0;

    const isWordPress = percentTrial >= Number(thresholdValue);

    const chartOptions: ApexOptions = useMemo(() => {
        const categories = chartData.map((item) => item.label);

        return {
            chart: {
                type: 'bar',
                stacked: false,
                toolbar: { show: false },
            },
            xaxis: {
                categories,
                labels: { style: FONT_STYLE },
            },
            yaxis: {
                title: { text: t('NumberWebsites'), style: FONT_STYLE },
                labels: { style: FONT_STYLE },
            },
            legend: {
                position: 'top',
                labels: { colors: '#555' },
            },
            colors: BAR_COLORS,
            tooltip: {
                shared: true,
                intersect: false,
            },
            plotOptions: {
                bar: {
                    borderRadius: 2,
                    columnWidth: '40%',
                },
            },
        };
    }, [chartData, t]);

    const chartSeries = useMemo(
        () => [
            {
                name: t('Paid'),
                data: chartData.map((item) => item.paid),
            },
            {
                name: t('Trial'),
                data: chartData.map((item) => item.trial),
            },
        ],
        [chartData, t]
    );

    return (
        <>
            <Helmet>
                <title>{t('dashboards')}</title>
            </Helmet>
            <ContentHeader title={t('dashboards')} />

            {isWordPress && (
                <div className="card bg-danger text-white my-2">
                    <div className="card-body">
                        <h4 className="card-title text-white">{t('Warning')}</h4>
                        <p className="card-text">
                            {t('The number of trial websites today is over 75%')} ({percentTrial.toFixed(1)}%)
                        </p>
                    </div>
                </div>
            )}

            <div className="content-body">
                <div className="col-12">
                    <SearchDashboardForm isLoading={isLoading || isRefetching} defaultOpen={true} />
                </div>

                {isLoading || isRefetching ? (
                    <Spinner />
                ) : (
                    <div className="card my-2">
                        <div className="card-body bg-white">
                            <Chart options={chartOptions} series={chartSeries} type="bar" height={600} />
                        </div>
                    </div>
                )}
            </div>
        </>
    );
}
