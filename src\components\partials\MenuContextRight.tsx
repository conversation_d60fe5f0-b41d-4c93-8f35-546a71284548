import classNames from 'classnames';
import { useLayoutEffect, useRef, useState } from 'react';
import { Download, Grid, PlusCircle, Send, Upload } from 'react-feather';
import { Link } from 'react-router-dom';
import { ItemLink } from 'types/common/Item';
interface IProps {
    contextMenu: ItemLink[];
}

export default function MenuContextRight({ contextMenu }: Readonly<IProps>) {
    const [show, setShow] = useState(false);
    const [translateX, setTranslateX] = useState(0);
    const refParent = useRef(null);
    const refChild = useRef(null);
    useLayoutEffect(() => {
        function updateChildPosition() {
            const parentWidth = refParent.current ? refParent.current['offsetWidth'] : 0;
            const childWidth = refChild.current ? refChild.current['offsetWidth'] : 0;
            if (parentWidth > 0 && childWidth > 0) {
                setTranslateX(parentWidth - childWidth);
            }
        }
        window.addEventListener('resize', updateChildPosition);
        updateChildPosition();
        return () => window.removeEventListener('resize', updateChildPosition);
    }, [show]);

    const handleClickMenu = (menu: ItemLink) => {
        if (menu.fnCallBack) {
            menu.fnCallBack.actionMenu(menu.id ?? 0);
            setShow(false);
        }
    };

    return (
        <div className="content-header-right text-end col-md-3 col-3 d-block">
            <div className="mb-1 breadcrumb-right" ref={refParent}>
                <div className="dropdown">
                    <button
                        className="btn-icon btn btn-primary btn-round btn-sm dropdown-toggle waves-effect waves-float waves-light"
                        type="button"
                        onClick={() => setShow((prevShow) => !prevShow)}
                    >
                        <Grid size={14} />
                    </button>
                    <div
                        className={classNames('dropdown-menu dropdown-menu-end', { show })}
                        ref={refChild}
                        style={{ transform: `translate(${translateX}px, 40px)` }}
                    >
                        {contextMenu.map((menu: ItemLink, index: number) => {
                            let iconJSX;
                            if (menu.icon) {
                                switch (menu.icon) {
                                    case 'PLUS':
                                        iconJSX = <PlusCircle size={14} className="me-1" />;
                                        break;
                                    case 'DOWNLOAD':
                                        iconJSX = <Download size={14} className="me-1" />;
                                        break;
                                    case 'UPLOAD':
                                        iconJSX = <Upload size={14} className="me-1" />;
                                        break;
                                    default:
                                        break;
                                }
                            }
                            return menu.to ? (
                                <Link key={index} to={menu.to} className="dropdown-item">
                                    {iconJSX}
                                    <span className="align-middle">{menu.text}</span>
                                </Link>
                            ) : (
                                <a key={index} onClick={() => handleClickMenu(menu)} className="dropdown-item">
                                    {iconJSX}
                                    <span className="align-middle">{menu.text}</span>
                                </a>
                            );
                        })}
                    </div>
                </div>
            </div>
        </div>
    );
}
