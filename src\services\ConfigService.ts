import { gql } from 'graphql-request';

export const CONFIG_LIST = gql`
    query Configs_list($page: Int!, $limit: Int!, $filters: [String!], $sort: String, $search: String) {
        configs_list(body: { page: $page, limit: $limit, filters: $filters, sort: $sort, search: $search }) {
            totalCount
            totalPages
            currentPage
            data {
                id
                code
                name
                value
            }
        }
    }
`;

export const CONFIG_UPDATE = gql`
    mutation Configs_update($id: Int!, $body: ConfigUpdateInputDto!) {
        configs_update(id: $id, body: $body) {
            id
        }
    }
`;
