import { baseFilt<PERSON><PERSON>onfig, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, DataList, FilterConfig } from './common';
import { FILTER_CONDITIONS } from '../constants/common';

export enum WebsiteStatus {
    WAITING = 1,
    INSTALLING = 2,
    INSTALLED = 3,
    ACTIVE = 4,
    EXPIRED = 5,
    DESTROYED = 6,
    DISABLED = 7,
}

export default interface Website extends BaseModel {
    domain: string;
    customer_id: number;
    custom_domain?: string;
    template_id: number;
    status_id: WebsiteStatus;
    template?: {
        id: string;
        name: string;
    };
    customer?: {
        id: number;
        full_name: string;
    };
}

export interface WebsiteQuery {
    websites_list: DataList<Website>;
}

export interface SearchWebsite extends BaseSearch {
    customer_id?: string;
    template_id?: string;
}

export type SearchWebsiteParam = {
    [key in keyof SearchWebsite]: string;
};

export interface WebsiteBackup extends BaseModel {
    website_id: number;
    file_id: number | null;
    file?: {
        file_name: string;
        file_url: string;
    };
    is_request: boolean;
}

export interface WebsiteBackup<PERSON><PERSON>y {
    backup_websites_list: DataList<WebsiteBackup>;
}

export interface CloudflareDNSRecord extends BaseModel {
    type: string;
    domain?: string;
    name: string;
    content: string;
    ttl: number;
    priority: number;
    proxied: boolean;
    zone_id: string;
    zone_name: string;
    created_on: string;
    modified_on: string;
}

export interface CloudflareDNSFormData {
    type: string;
    name: string;
    content: string;
    ttl: number;
    priority: number | null;
    proxied: boolean;
    domain?: string;
}

export interface WebsiteDnsQuery {
    cloudflare_get_dns_records: CloudflareDNSRecord[];
}

export const websiteFilterConfig: FilterConfig = {
    ...baseFilterConfig,
    customer_id: { key: 'customer_id', operator: FILTER_CONDITIONS.IN },
    template_id: { key: 'template_id', operator: FILTER_CONDITIONS.IN },
};

export interface WebsiteHealthCheck {
    websites_health_check: {
        success: boolean;
        domain: string;
        disabled: boolean;
        message: string;
        is_custom_domain: boolean;
    };
}
