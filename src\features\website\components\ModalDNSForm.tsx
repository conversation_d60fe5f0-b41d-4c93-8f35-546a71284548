import { useEffect, useLayoutEffect } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import classNames from 'classnames';
import { DNS_RECORD_TYPES, TTL_OPTIONS } from 'constants/common';
import { toggleModalOpen } from 'utils/common';
import UpdateButton from 'components/partials/UpdateButton';
import { useTranslation } from 'react-i18next';
import { CloudflareDNSFormData, CloudflareDNSRecord } from 'types/Website';

interface ModalDNSFormProps {
    show: boolean;
    onClose: () => void;
    mode: 'create' | 'update';
    initialData?: CloudflareDNSRecord;
    domain: string;
    isLoading?: boolean;
    onSubmit: (data: CloudflareDNSFormData) => void;
}

export default function ModalDNSForm({
    show,
    onClose,
    mode,
    initialData,
    domain,
    isLoading = false,
    onSubmit,
}: ModalDNSFormProps) {
    const { t } = useTranslation();
    useLayoutEffect(() => toggleModalOpen(show), [show]);

    const schema = yup.object({
        domain: yup.string().required(t('error.required')).trim(),
        type: yup.string().required(t('error.required')),
        name: yup.string().required(t('error.required')).trim(),
        content: yup.string().required(t('error.required')).trim(),
        ttl: yup.number().typeError(t('error.required')).required(t('error.required')),
        priority: yup
            .number()
            .typeError(t('error.required'))
            .nullable()
            .when('type', {
                is: (type: string) => ['MX', 'SRV'].includes(type),
                then: yup.number().required('Priority is required for MX and SRV records').min(0),
                otherwise: yup.number().nullable(),
            }),
        proxied: yup.boolean().required(),
    });

    const {
        register,
        handleSubmit,
        reset,
        watch,
        formState: { errors },
    } = useForm<CloudflareDNSFormData>({
        resolver: yupResolver(schema),
    });

    const selectedType = watch('type');

    useEffect(() => {
        if (show) {
            reset({
                domain: initialData?.domain ?? domain,
                type: initialData?.type || '',
                name: initialData?.name || '',
                content: initialData?.content || '',
                ttl: initialData?.ttl ?? 3600,
                priority: initialData?.priority ?? null,
                proxied: initialData?.proxied ?? false,
            });
        }
    }, [show, domain, initialData, reset]);

    const handleFormSubmit = (data: CloudflareDNSFormData) => {
        const payload: CloudflareDNSFormData = {
            ...data,
            ttl: Number(data.ttl),
            priority: showPriorityField ? Number(data.priority) : null,
            proxied: Boolean(data.proxied),
        };

        onSubmit(payload);
    };

    const showPriorityField = ['MX', 'SRV'].includes(selectedType);

    return (
        <>
            <div
                className={classNames('modal fade text-start modal-primary', { show })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-dialog-centered modal-lg">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">
                                {mode === 'create' ? 'Create DNS Record' : 'Update DNS Record'}
                            </h5>
                            <button type="button" className="btn-close" onClick={onClose} />
                        </div>
                        <form onSubmit={handleSubmit(handleFormSubmit)} className="pt-50">
                            <div className="modal-body">
                                <div className="row">
                                    <div className="col-md-6 mb-1">
                                        <label className="form-label">
                                            Domain <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('domain')}
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.domain?.message),
                                            })}
                                            readOnly={true}
                                        />
                                        <span className="error">{String(errors.domain?.message || '')}</span>
                                    </div>

                                    <div className="col-md-6 mb-1">
                                        <label className="form-label">
                                            Type <span className="error">*</span>
                                        </label>
                                        <select
                                            {...register('type')}
                                            className={classNames('form-select', {
                                                'is-invalid': Boolean(errors.type?.message),
                                            })}
                                        >
                                            <option value="">-- Select Type --</option>
                                            {DNS_RECORD_TYPES.map((opt) => (
                                                <option key={opt.type} value={opt.type}>
                                                    {opt.type} - {opt.description}
                                                </option>
                                            ))}
                                        </select>
                                        <span className="error">{String(errors.type?.message || '')}</span>
                                    </div>

                                    <div className="col-md-6 mb-1">
                                        <label className="form-label">
                                            Name <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('name')}
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.name?.message),
                                            })}
                                            placeholder="www, mail, @"
                                        />
                                        <span className="error">{String(errors.name?.message || '')}</span>
                                    </div>

                                    <div className="col-md-6 mb-1">
                                        <label className="form-label">
                                            Content <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('content')}
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.content?.message),
                                            })}
                                            placeholder="IP address or target"
                                        />
                                        <span className="error">{String(errors.content?.message || '')}</span>
                                    </div>

                                    <div className="col-md-6 mb-1">
                                        <label className="form-label">
                                            TTL <span className="error">*</span>
                                        </label>
                                        <select
                                            {...register('ttl')}
                                            className={classNames('form-select', {
                                                'is-invalid': Boolean(errors.ttl?.message),
                                            })}
                                        >
                                            <option value="">-- Select TTL --</option>
                                            {TTL_OPTIONS.map((option) => (
                                                <option key={option.value} value={option.value}>
                                                    {option.label}
                                                </option>
                                            ))}
                                        </select>
                                        <span className="error">{String(errors.ttl?.message || '')}</span>
                                        <small className="form-text text-muted">
                                            Lower TTL = faster updates, Higher TTL = better performance
                                        </small>
                                    </div>

                                    {showPriorityField && (
                                        <div className="col-md-6 mb-1">
                                            <label className="form-label">
                                                Priority <span className="error">*</span>
                                            </label>
                                            <input
                                                {...register('priority')}
                                                type="number"
                                                className={classNames('form-control', {
                                                    'is-invalid': Boolean(errors.priority?.message),
                                                })}
                                                placeholder="10"
                                            />
                                            <span className="error">{String(errors.priority?.message || '')}</span>
                                        </div>
                                    )}

                                    <div className="col-md-6 mb-1">
                                        <label className="form-label">Proxy Status</label>
                                        <select {...register('proxied')} className="form-select">
                                            <option value="false">DNS Only</option>
                                            <option value="true">Proxied</option>
                                        </select>
                                        <small className="form-text text-muted">
                                            Proxied records benefit from Cloudflare's performance and security features
                                        </small>
                                    </div>
                                </div>
                            </div>
                            <div className="modal-footer">
                                <UpdateButton
                                    btnText={mode === 'update' ? t('update') : t('create')}
                                    isLoading={isLoading}
                                    hasDivWrap={false}
                                />
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}
        </>
    );
}
