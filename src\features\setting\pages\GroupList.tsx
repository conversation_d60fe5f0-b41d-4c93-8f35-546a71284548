import classNames from 'classnames';
import ModalConfirm from 'components/partials/ModalConfirm';
import ModalItemParamUpdate from 'components/partials/ModalItemParamUpdate';
import { LIMIT_DEFAULT, PAGE_NUMBER_DEFAULT, QUERY_KEY, SUCCESS_MESSAGE } from 'constants/common';
import { useMemo, useState } from 'react';
import { Edit, Menu, PlusCircle, Search, Trash2, X } from 'react-feather';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import PerfectScrollbar from 'react-perfect-scrollbar';
import 'react-perfect-scrollbar/dist/css/styles.css';
import { ItemParamModel, ItemParamType } from 'types/common/Item';
import { getFieldInArrayObject, showToast } from 'utils/common';
import ListGroupAction from '../components/ListGroupAction';
import './../../../components/partials/chat/app-chat-list.css';
import './../../../components/partials/chat/app-chat.css';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import Group, { GroupCreate, GroupQuery, GroupUpdate } from '../../../types/Group';
import { GROUPS_ACTIONS, GROUP_CREATE, GROUP_DELETE, GROUP_LIST, GROUP_UPDATE } from '../../../services/GroupService';
import { ActionQuery } from '../../../types/Action';
import { ACTION_LIST, getFlatActions } from '../../../services/ActionService';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import { isEmpty } from 'lodash';
import { BaseSearch } from '../../../types/common';

export default function GroupList() {
    const [searchText, setSearchText] = useState('');
    const [show, setShow] = useState(false);
    const [showUpdate, setShowUpdate] = useState(false);
    const [showDelete, setShowDelete] = useState(false);
    const [itemId, setItemId] = useState(0);
    const [itemEditId, setItemEditId] = useState(0);
    const { t } = useTranslation();

    const { data: groupData, refetch } = useGraphQLQuery<GroupQuery, BaseSearch>([QUERY_KEY.GROUPS], GROUP_LIST, {
        limit: LIMIT_DEFAULT,
        page: PAGE_NUMBER_DEFAULT,
        search: searchText,
    });
    const groups = groupData?.groups_list?.data;
    const itemParam = useMemo(
        () => groups?.find((item) => item.id === itemEditId) as ItemParamModel | undefined,
        [groups, itemEditId]
    );
    const actionIds = useMemo(
        () => groups?.find((item) => item.id === itemId)?.actions?.map((action) => action?.id?.toString() ?? '') ?? [],
        [groups, itemId]
    );

    const { data: actionData } = useGraphQLQuery<ActionQuery, BaseSearch>([QUERY_KEY.USER_ACTIONS], ACTION_LIST, {
        limit: LIMIT_DEFAULT,
        page: PAGE_NUMBER_DEFAULT,
    });
    const actions = getFlatActions(actionData?.actions_list.data ?? []);

    const handleResponseGroup = (groupId: number) => {
        showToast(true, [SUCCESS_MESSAGE]);
        setShowUpdate(false);
        setItemId(groupId);
        setItemEditId(groupId);
        refetch();
    };

    const createGroupMutation = useGraphQLMutation<GroupCreate, Partial<Group>>(GROUP_CREATE, '', {
        onSuccess: (data) => {
            handleResponseGroup(data.groups_create?.id ?? 0);
        },
    });

    const updateGroupMutation = useGraphQLMutation<GroupUpdate, Partial<Group>>(GROUP_UPDATE, '', {
        onSuccess: (data) => {
            handleResponseGroup(data.groups_update?.id ?? 0);
        },
    });

    const deleteMutation = useGraphQLMutation(GROUP_DELETE, '', {
        onSuccess: () => {
            showToast(true, [SUCCESS_MESSAGE]);
            setShowDelete(false);
            setItemId(0);
            setItemEditId(0);
            refetch();
        },
    });

    const updateGroupActionMutation = useGraphQLMutation<
        { groups_actions: boolean },
        { id: number; action_ids: number[] }
    >(GROUPS_ACTIONS, '', {
        onSuccess: () => {
            showToast(true, [SUCCESS_MESSAGE]);
            refetch();
        },
    });

    const onChooseGroup = (groupId: number) => {
        setItemId(groupId);
        setItemEditId(groupId);
        updateGroupActionMutation.reset();
    };

    const handleUpdate = (isAdd: boolean) => {
        if (isAdd) setItemEditId(0);
        createGroupMutation.reset();
        updateGroupMutation.reset();
        setShowUpdate(true);
    };

    const handleDelete = () => {
        deleteMutation.reset();
        setShowDelete(true);
    };

    const updateItem = async (data: ItemParamModel) =>
        itemEditId
            ? updateGroupMutation.mutate({
                  name: data.name,
                  id: itemEditId,
                  desc: isEmpty(data.desc) ? null : data.desc,
              })
            : createGroupMutation.mutate({ name: data.name, desc: isEmpty(data.desc) ? null : data.desc });

    const deleteItem = async () => {
        if (itemEditId) {
            deleteMutation.mutate({ id: itemEditId });
        }
    };

    const grantActions = (ids: string[]) => {
        updateGroupActionMutation.mutate({ id: itemId, action_ids: ids.map((id) => Number(id)) });
    };

    return (
        <>
            <Helmet>
                <title>{t('group.multiple')}</title>
            </Helmet>
            <div className="sidebar-left">
                <div className="sidebar">
                    <div className={classNames('sidebar-content', { show })}>
                        <span className="sidebar-close-icon" onClick={() => setShow(false)}>
                            <X size={14} />
                        </span>
                        <div className="chat-fixed-search">
                            <div className="d-flex align-items-center w-100">
                                <div className="sidebar-profile-toggle">
                                    <PlusCircle
                                        size={24}
                                        className="text-primary cursor-pointer"
                                        onClick={() => handleUpdate(true)}
                                    />
                                </div>
                                <div className="input-group input-group-merge ms-1 w-100">
                                    <span className="input-group-text round">
                                        <Search size={14} className="text-muted" />
                                    </span>
                                    <input
                                        type="text"
                                        className="form-control round"
                                        placeholder={t('keywords')}
                                        value={searchText}
                                        onChange={(e) => setSearchText(e.target.value.trim())}
                                    />
                                </div>
                            </div>
                        </div>
                        <PerfectScrollbar className="chat-user-list-wrapper list-group">
                            <ul className="chat-users-list chat-list media-list">
                                {(groups ?? [])
                                    .filter((item) => item.name.toLowerCase().includes(searchText.toLowerCase()))
                                    .map((item) => (
                                        <li
                                            key={item.id}
                                            className={classNames({ active: item.id === itemId })}
                                            onClick={() => onChooseGroup(item.id ?? 0)}
                                        >
                                            <div className="chat-info flex-grow-1">
                                                <h5 className="mb-0">{item.name}</h5>
                                            </div>
                                        </li>
                                    ))}
                            </ul>
                        </PerfectScrollbar>
                    </div>
                </div>
            </div>
            {!!itemId && (
                <div className="content-right w-100">
                    <div className="content-wrapper container-xxl p-0">
                        <div className="content-header row" />
                        <div className="content-body">
                            <div className="body-content-overlay" />
                            <section className="chat-app-window">
                                <div className="active-chat">
                                    <div className="chat-navbar">
                                        <header className="chat-header">
                                            <div className="d-flex align-items-center">
                                                <div
                                                    className="sidebar-toggle d-block d-lg-none me-1"
                                                    onClick={() => setShow(true)}
                                                >
                                                    <Menu size={14} />
                                                </div>
                                                <h6
                                                    className="mb-0 text-primary cursor-pointer"
                                                    onClick={() => handleUpdate(false)}
                                                >
                                                    {getFieldInArrayObject(groups ?? [], itemId)} <Edit size={14} />
                                                </h6>
                                            </div>
                                            <div className="d-flex align-items-center">
                                                <button
                                                    type="button"
                                                    title={t('delete')}
                                                    className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                                    onClick={handleDelete}
                                                >
                                                    <Trash2 size={14} />
                                                </button>
                                            </div>
                                        </header>
                                    </div>
                                    <ListGroupAction
                                        actions={actions ?? []}
                                        actionIds={actionIds ?? []}
                                        loading={updateGroupActionMutation.isPending}
                                        handleSubmit={grantActions}
                                    />
                                </div>
                            </section>
                        </div>
                    </div>
                </div>
            )}
            <ModalItemParamUpdate
                itemTypeId={ItemParamType.GROUP}
                show={showUpdate}
                itemParam={itemParam}
                isLoading={createGroupMutation.isPending || updateGroupMutation.isPending}
                changeShow={(s: boolean) => setShowUpdate(s)}
                submitAction={updateItem}
            />
            <ModalConfirm
                show={showDelete}
                text={t('confirm.delete', { data: 'group' })}
                btnDisabled={deleteMutation.isPending}
                changeShow={(s: boolean) => setShowDelete(s)}
                submitAction={deleteItem}
            />
        </>
    );
}
