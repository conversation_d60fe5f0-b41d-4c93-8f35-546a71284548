import { baseFilterConfig, BaseModel, BaseSearch, FilterConfig } from './common';
import { ItemParam, ItemStatus } from './common/Item';
import { FILTER_CONDITIONS } from '../constants/common';

interface SubscriptionPlanDefault<T = { id: number; value: string }> extends BaseModel {
    name: string;
    status_id: ItemStatus;
    desc?: string;
    price: number;
    features?: T[];
    display_order: number;
    type_id: number;
    trial_days?: number;
}

// Định nghĩa type cho hai trường hợp cụ thể
export interface SubscriptionPlan extends SubscriptionPlanDefault<{ id: number; value: string | number | boolean }> {}
export interface SubscriptionPlanFormData
    extends SubscriptionPlanDefault<{ label: string; type: string; value: string | number | boolean }> {}

export interface SearchSubscriptionPlan extends BaseSearch {
    type_id?: string;
}

export type SearchSubscriptionPlanParam = {
    [key in keyof SearchSubscriptionPlan]: string;
};

export const subscriptionPlanFilterConfig: FilterConfig = {
    ...baseFilterConfig,
    type_id: { key: 'type_id', operator: FILTER_CONDITIONS.IN },
};

export interface SubscriptionPlanDetail {
    subscription_plan_view: SubscriptionPlan;
}

export interface SubscriptionPlanQuery {
    subscription_plan_list: SubscriptionPlan[];
}

export enum ItemPlanType {
    TRIAL = 1,
    BASIC = 2,
    PREMIUM = 3,
}

export const ItemPlanTypeNames: ItemParam[] = [
    { id: ItemPlanType.TRIAL, name: 'trial', className: 'badge badge-glow bg-primary' },
    { id: ItemPlanType.BASIC, name: 'basic', className: 'badge badge-glow bg-success' },
    { id: ItemPlanType.PREMIUM, name: 'premium', className: 'badge badge-glow bg-gradient' },
];

export interface IFeatureItem {
    label: string;
    desc?: string;
    type: string;
    unit?: string;
    options?: string[];
}

export const FeatureList: IFeatureItem[] = [
    { label: 'Storage Limit', type: 'number', unit: 'GB' },
    {
        label: 'Custom Domain Enabled',
        type: 'checkbox',
        desc: 'Website address of your choice e.g. www.BestShoeEver.com',
    },
    { label: '1 Year Free Domain Enabled', type: 'checkbox' },
    { label: 'SSL Enabled', type: 'checkbox' },
    { label: 'AI Assisted Content Enabled', type: 'checkbox' },
    { label: 'Mobile Optimized Enabled', type: 'checkbox' },
    {
        label: 'Backup Frequency',
        type: 'dropdown',
        options: ['None', 'Daily', 'Weekly', 'Every 10 days'],
        desc: 'Frequency',
    },
    { label: 'Ads Enabled', type: 'checkbox', desc: 'Show Weaveform Ads' },
    { label: 'Contact Form Enabled', type: 'checkbox' },
    { label: 'Firewall/Scanner Enabled', type: 'checkbox' },
    {
        label: 'Customer Care Level',
        type: 'dropdown',
        options: ['Nil', 'Email (1 day)', 'Priority Support'],
        desc: 'Support Level',
    },
];
