/**
 * Custom styles for form elements
 */

/* Input group text styles for password toggle */
.input-group-merge .input-group-text {
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

/* Error state - Ưu tiên cao nhất */
.form-control.error + .input-group-text,
.form-control.error ~ .input-group-text {
  border-color: #ea5455 !important;
}

/* Focus state - Chỉ áp dụng khi không có lỗi */
.form-control:not(.error):focus + .input-group-text,
.form-control:not(.error):focus ~ .input-group-text {
  border-color: #7367f0 !important;
}

/* Hover state - Chỉ áp dụng khi không có lỗi và không focus */
.input-group-merge:hover .form-control:not(.error):not(:focus) ~ .input-group-text,
.input-group-merge:hover .form-control:not(.error):not(:focus) + .input-group-text {
  border-color: #b8b8b8;
}

/* Disabled state */
.form-control:disabled + .input-group-text,
.form-control[readonly] + .input-group-text {
  background-color: #efefef;
  border-color: #d8d6de;
}

/* Dark theme support */
.dark-layout .input-group-text {
  background-color: #283046;
  border-color: #404656;
}

.dark-layout .form-control:not(.error):focus + .input-group-text,
.dark-layout .form-control:not(.error):focus ~ .input-group-text {
  border-color: #7367f0 !important;
}

.dark-layout .form-control.error + .input-group-text,
.dark-layout .form-control.error ~ .input-group-text {
  border-color: #ea5455 !important;
}

/* Đảm bảo error state luôn được ưu tiên cao nhất */
.form-control.error:focus + .input-group-text,
.form-control.error:focus ~ .input-group-text {
  border-color: #ea5455 !important;
}

/* Master Tag Import Drag & Drop Area */
.border-dashed {
  border-style: dashed !important;
  border-width: 2px !important;
}

.drag-drop-area {
  transition: all 0.3s ease;
  cursor: pointer;
}

.drag-drop-area:hover {
  border-color: #7367f0 !important;
  background-color: #f8f9fa;
}

.drag-drop-area.active {
  border-color: #7367f0 !important;
  background-color: #e7e7ff;
  transform: scale(1.02);
}

.import-success-stats {
  border-radius: 8px;
  padding: 1rem;
  margin: 0.5rem 0;
}

.import-error-list {
  max-height: 200px;
  overflow-y: auto;
}
