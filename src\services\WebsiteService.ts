import { gql } from 'graphql-request';

export const WEBSITE_LIST = gql`
    query Websites_list($page: Int, $limit: Int) {
        websites_list(input: { page: $page, limit: $limit }) {
            totalCount
            totalPages
            currentPage
            data {
                id
                created_by
                updated_by
                created_at
                updated_at
                customer_id
                template_id
                domain
                custom_domain
                status_id
                template {
                    id
                    name
                }
                customer {
                    id
                    full_name
                }
            }
        }
    }
`;

export const UPDATE_WEBSITE_STATUS = gql`
    mutation Websites_update_status($id: Int!, $input: WebsiteUpdateStatusInputDto!) {
        websites_update_status(id: $id, input: $input) {
            id
            created_by
            updated_by
            created_at
            updated_at
            customer_id
            template_id
            domain
            custom_domain
            info
            status_id
            is_dead
        }
    }
`;

export const BACKUP_WEBSITES_RESTORE = gql`
    mutation backup_websites_update_restore_status($id: Int!) {
        backup_websites_update_restore_status(id: $id) {
            id
            is_process_restore
            is_request
            updated_at
        }
    }
`;

export const WEBSITES_WPCLI = gql`
    mutation Websites_wpcli($input: WebsiteWpcliInputDto!) {
        websites_wpcli(input: $input)
    }
`;

export const WEBSITE_HEALTH_CHECK = gql`
    mutation Websites_health_check($id: Int!) {
        websites_health_check(id: $id)
    }
`;
