import ContentHeader from 'components/partials/ContentHeader';
import Spinner from 'components/partials/Spinner';
import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import { CONFIG_LIST, CONFIG_UPDATE } from 'services/ConfigService';
import { PAGINATION, QUERY_KEY, SUCCESS_MESSAGE } from 'constants/common';
import { convertPaging, generateFilters, showToast } from 'utils/common';
import useQueryParams from 'hooks/useQueryParams';
import { keepPreviousData } from '@tanstack/react-query';
import { useGraphQLMutation } from 'hooks/useGraphQLMutation';
import PaginationTable from 'components/partials/PaginationTable';
import { Helmet } from 'react-helmet-async';
import ListConfig from '../components/ListConfig';
import Config, { ConfigQuery } from 'types/Config';
import ModalConfigUpdate from '../components/ModalConfigUpdate';
import isUndefined from 'lodash/isUndefined';
import { baseFilterConfig, BaseSearchParam } from 'types/common';
import omitBy from 'lodash/omitBy';

export default function ConfigList() {
    const { t } = useTranslation();
    const [showModal, setShowModal] = useState(false);
    const [selectedConfig, setSelectedConfig] = useState<Config | undefined>(undefined);
    const { queryParams, setQueryParams } = useQueryParams<BaseSearchParam>();

    const paramConfig: BaseSearchParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
        },
        isUndefined
    );
    const { limit, page, ...dataParamConfig } = paramConfig;
    const filters = generateFilters(dataParamConfig, baseFilterConfig);

    const { data, isLoading, isRefetching, refetch } = useGraphQLQuery<ConfigQuery>(
        [QUERY_KEY.CONFIGS, paramConfig, filters],
        CONFIG_LIST,
        {
            page: Number(paramConfig.page),
            limit: Number(paramConfig.limit),
            filters: filters.length > 0 ? filters : undefined,
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );

    const updateMutation = useGraphQLMutation(CONFIG_UPDATE, '', {
        onSuccess: () => {
            showToast(true, [SUCCESS_MESSAGE]);
            setShowModal(false);
            refetch();
        },
    });

    const handleEdit = (id: number) => {
        const config = data?.configs_list.data.find((item) => item.id === id);
        setSelectedConfig(config);
        setShowModal(true);
    };

    const handleSubmit = (value: string) => {
        if (!selectedConfig) return;
        updateMutation.mutate({ id: selectedConfig.id!, body: { value } });
    };

    const handlePageChange = (_: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    return (
        <>
            <Helmet>
                <title>Config</title>
            </Helmet>
            <ContentHeader title="System Configs" />
            <div className="content-body">
                {(isLoading || isRefetching) && <Spinner />}
                {!isLoading && !isRefetching && (
                    <div className="card">
                        <ListConfig
                            items={data?.configs_list.data ?? []}
                            paging={convertPaging(data?.configs_list, paramConfig)}
                            handleEdit={handleEdit}
                        />
                        <PaginationTable
                            countItem={data?.configs_list.totalCount}
                            totalPage={data?.configs_list.totalPages}
                            currentPage={data?.configs_list.currentPage}
                            handlePageChange={handlePageChange}
                        />
                    </div>
                )}
                <ModalConfigUpdate
                    show={showModal}
                    changeShow={setShowModal}
                    config={selectedConfig}
                    isLoading={updateMutation.isPending}
                    submitAction={handleSubmit}
                />
            </div>
        </>
    );
}
