import classNames from 'classnames';
import UpdateButton from 'components/partials/UpdateButton';
import { useEffect, useLayoutEffect } from 'react';
import { useForm } from 'react-hook-form';
import Ticket, { TicketStatusNames, TicketTypeNames } from 'types/Ticket';
import { selectItem, toggleModalOpen } from 'utils/common';
import { useTranslation } from 'react-i18next';

interface IProps {
    show: boolean;
    ticket?: Ticket;
    isLoading?: boolean;
    changeShow: (s: boolean) => void;
    submitAction: (data: Ticket) => void;
}

export default function ModalTicketUpdate({ show, ticket, isLoading, changeShow, submitAction }: Readonly<IProps>) {
    const { t } = useTranslation();
    useLayoutEffect(() => toggleModalOpen(show), [show]);

    const {
        register,
        handleSubmit,
        reset,
        formState: { errors },
    } = useForm<Ticket>();

    useEffect(() => {
        if (ticket && show) {
            reset(ticket);
        }
    }, [ticket, show, reset]);

    return (
        <>
            <div
                className={classNames('modal fade text-start modal-primary', { show })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-dialog-centered">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">{ticket ? 'Edit Ticket' : 'Add Ticket'}</h5>
                            <button type="button" className="btn-close" onClick={() => changeShow(false)} />
                        </div>
                        <form className="validate-form pt-50" onSubmit={handleSubmit(submitAction)}>
                            <div className="modal-body">
                                <div className="row">
                                    <div className="col-12 mb-1">
                                        <label className="form-label">Title</label>
                                        <input
                                            {...register('title')}
                                            type="text"
                                            className={classNames('form-control')}
                                            readOnly={true}
                                        />
                                    </div>
                                    <div className="col-12 mb-1">
                                        <label className="form-label">Customer</label>
                                        <input
                                            {...register('customer.full_name')}
                                            type="text"
                                            className={classNames('form-control')}
                                            readOnly={true}
                                        />
                                    </div>
                                    <div className="col-12 mb-1">
                                        <label className="form-label">Content</label>
                                        <textarea
                                            {...register('content')}
                                            rows={2}
                                            className={classNames('form-control')}
                                            readOnly={true}
                                        />
                                    </div>
                                    <div className="col-12 mb-1">
                                        <label className="form-label">Feedback</label>
                                        <textarea
                                            {...register('feedback')}
                                            rows={2}
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.feedback?.message),
                                            })}
                                        />
                                    </div>
                                    <div className="col-12 mb-1">
                                        <label className="form-label">Type</label>
                                        <select
                                            {...register('type_id', { valueAsNumber: true })}
                                            className="form-select"
                                            disabled
                                        >
                                            {selectItem(TicketTypeNames, t, true)}
                                        </select>
                                    </div>
                                    <div className="col-12 mb-1">
                                        <label className="form-label">Status</label>
                                        <select
                                            {...register('status_id', { valueAsNumber: true })}
                                            className="form-select"
                                        >
                                            {selectItem(TicketStatusNames, t, true)}
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div className="modal-footer">
                                <UpdateButton btnText={t('update')} isLoading={isLoading} hasDivWrap={false} />
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}
        </>
    );
}
