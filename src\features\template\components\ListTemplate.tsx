import UserLink from 'components/partials/UserLink';
import { Edit, Trash2 } from 'react-feather';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import Template, { TemplateType } from 'types/Template';
import { ItemStatus, ItemStatusNames } from 'types/common/Item';
import { genTableIndex, getFieldHtml } from 'utils/common';
import { Paging } from '../../../types/common';

interface IProps {
    items: Template[];
    typeId: number;
    paging: Paging;
    handleDelete: (id: number) => void;
}

export default function ListTemplate({ items, typeId, paging, handleDelete }: Readonly<IProps>) {
    const { t } = useTranslation();

    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="text-center">No</th>
                        <th>Name</th>
                        {typeId === TemplateType.LIBRARY && <th>Designer</th>}
                        <th>Industries</th>
                        <th>Multiple Page</th>
                        <th className="text-center">Status</th>
                        <th className="thAction1"></th>
                    </tr>
                </thead>
                <tbody>
                    {items.map((item: Template, index: number) => (
                        <tr key={item.id}>
                            <td className="text-center">{genTableIndex(index, paging.limit, paging.current_page)}</td>
                            <td>
                                <UserLink
                                    to={`/template/edit/${item.id}`}
                                    avatar={item.image?.file_url}
                                    name={item.name}
                                />
                            </td>
                            {typeId === TemplateType.LIBRARY && (
                                <td>
                                    {item.designer && (
                                        <UserLink
                                            to={`/user/edit/${item.designer.id}`}
                                            avatar={item.designer.avatar?.file_url}
                                            name={item.designer.full_name}
                                        />
                                    )}
                                </td>
                            )}
                            <td>{item.industries?.map((item) => item.name).join(', ')}</td>
                            <td className="text-center">
                                <div className="form-check">
                                    <input
                                        className="form-check-input"
                                        type="checkbox"
                                        id={`multiple-page-${item.id}`}
                                        checked={item.is_multiple}
                                        readOnly
                                        disabled
                                    />
                                </div>
                            </td>
                            <td className="text-center">{getFieldHtml(ItemStatusNames, item.status_id, t)}</td>
                            <td className="text-center">
                                {item.status_id !== ItemStatus.ACTIVE && item.is_kit && (
                                    <button
                                        type="button"
                                        title={t('delete')}
                                        className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                        onClick={() => handleDelete(item.id!)}
                                    >
                                        <Trash2 size={14} />
                                    </button>
                                )}
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
