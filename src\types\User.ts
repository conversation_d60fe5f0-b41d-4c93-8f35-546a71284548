import { FILTER_CONDITIONS } from '../constants/common';
import Group from './Group';
import Industry from './Industry';
import { SmtpConfig } from './Smtp';
import { baseFilterConfig, BaseModel, BaseSearch, DataList, FilterConfig } from './common';
import { ItemFile, ItemParam, ItemStatus } from './common/Item';

export enum Gender {
    MALE = 1,
    FEMALE = 2,
    OTHER = 3,
}

export enum UserRole {
    ADMIN = 1,
    CUSTOMER = 2,
    DESIGNER = 3,
}

export interface LoginData {
    email: string;
    password: string;
    role_id: number;
}

export interface ForgotPasswordData extends Omit<LoginData, 'password'> {}

export interface ChangePassword {
    password: string;
    old_password: string;
    confirm_password: string;
    token?: string;
}

export interface ChangePasswordToken {
    password: string;
    confirm_password: string;
    token?: string;
    otp: string;
}

export interface SearchUser extends BaseSearch {
    role_id?: string;
}

export type SearchUserParam = {
    [key in keyof SearchUser]: string;
};

export const UserRoleNames: ItemParam[] = [
    { id: UserRole.ADMIN, name: 'admin' },
    { id: UserRole.CUSTOMER, name: 'customer' },
    { id: UserRole.DESIGNER, name: 'designer' },
];

export const GenderNames: ItemParam[] = [
    { id: Gender.MALE, name: 'male' },
    { id: Gender.FEMALE, name: 'female' },
    { id: Gender.OTHER, name: 'other' },
];

export const displayUserName = (user: User | null | undefined) => {
    if (user) {
        if (user.first_name && user.last_name) return `${user.first_name} ${user.last_name}`;
        if (user.first_name) return user.first_name;
        return user.last_name ?? '';
    }
    return '';
};

export default interface User extends BaseModel {
    first_name: string;
    last_name?: string;
    full_name?: string;
    phone_number: string;
    email: string;
    password: string;
    avatar_id?: number;
    avatar?: ItemFile;
    role_id: UserRole;
    status_id: ItemStatus;
    gender_id: Gender;
    birthday?: string;
    address: string;
    token: {
        access_token: string;
        refresh_token: string;
    };
    uid: string;
    confirm_password?: string;
    industry_id?: number | null;
    groups?: Group[];
    bio?: string;
    industry?: Industry | null;
    smtp_config?: SmtpConfig;
}

export interface UserQuery {
    auth_login: User;
}

export interface ProfileQuery {
    auth_profile: User;
}

export interface LogoutQuery {
    auth_logout: boolean;
}

export interface RefreshQuery {
    auth_refresh: User;
}

export interface UserListQuery {
    users_list: DataList<User>;
}

export const userFilterConfig: FilterConfig = {
    ...baseFilterConfig,
    role_id: { key: 'role_id', operator: FILTER_CONDITIONS.EQUAL },
};

export interface UserCreate {
    users_create: User;
}

export interface UserUpdate {
    users_update: User;
}

export interface UserDetail {
    users_detail: User;
}

export interface UserRestPass {
    users_reset_pass: boolean;
}

export interface UserChangeGroup {
    users_change_groups: boolean;
}

export interface AuthUpdateRes {
    auth_update: User;
}

export interface UserForgotPass {
    auth_forgot_pass: string;
}
