import Spinner from 'components/partials/Spinner';
import { PAGINATION, QUERY_KEY } from 'constants/common';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import { Helmet } from 'react-helmet-async';
import ListTransaction from '../components/ListTransaction';
import { convertPaging, generateFilters } from '../../../utils/common';
import useQueryParams from '../../../hooks/useQueryParams';
import omitBy from 'lodash/omitBy';
import isUndefined from 'lodash/isUndefined';
import { keepPreviousData } from '@tanstack/react-query';
import ContentHeader from 'components/partials/ContentHeader';
import PaginationTable from '../../../components/partials/PaginationTable';
import SearchTransactionForm from '../components/SearchTransactionForm';
import { TRANSACTION_LIST } from 'services/TransactionService';
import Transaction, { SearchTransactionParam, transactionFilterConfig, TransactionQuery } from 'types/Transaction';

export default function TransactionList() {
    const { queryParams, setQueryParams } = useQueryParams<SearchTransactionParam>();
    const paramConfig: SearchTransactionParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            type_id: queryParams.type_id,
            plan_id: queryParams.plan_id,
            customer_id: queryParams.customer_id,
        },
        isUndefined
    );

    const { search, limit, page, ...dataParamConfig } = paramConfig;
    const filters = generateFilters({ ...dataParamConfig, search }, transactionFilterConfig);

    const { data, isLoading, isRefetching } = useGraphQLQuery<TransactionQuery>(
        [QUERY_KEY.TRANSACTIONS, paramConfig, filters],
        TRANSACTION_LIST,
        {
            page: Number(page),
            limit: limit,
            filters: filters.length > 0 ? filters : undefined,
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    return (
        <>
            <Helmet>
                <title>Transactions</title>
            </Helmet>
            <ContentHeader title="Transactions" />
            <div className="content-body">
                <div className="col-12">
                    <SearchTransactionForm isLoading={isLoading || isRefetching} />
                    {(isLoading || isRefetching) && <Spinner />}
                    {!isLoading && !isRefetching && (
                        <div className="card">
                            <ListTransaction
                                items={data?.transactions_list.data ?? []}
                                paging={convertPaging<Transaction, SearchTransactionParam>(
                                    data?.transactions_list,
                                    paramConfig
                                )}
                            />
                            <PaginationTable
                                countItem={data?.transactions_list.totalCount}
                                totalPage={data?.transactions_list.totalPages}
                                currentPage={data?.transactions_list.currentPage}
                                handlePageChange={handlePageChange}
                            />
                        </div>
                    )}
                </div>
            </div>
        </>
    );
}
