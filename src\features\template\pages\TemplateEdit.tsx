import ContentHeader from 'components/partials/ContentHeader';
import { Helmet } from 'react-helmet-async';
import { Navigate, useParams } from 'react-router-dom';
import UpdateTemplateForm from '../components/UpdateTemplateForm';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { PATH, QUERY_KEY } from '../../../constants/common';
import { TEMPLATE_VIEW } from '../../../services/TemplateService';
import { TemplateViewResponse } from '../../../types/Template';
import Spinner from '../../../components/partials/Spinner';

export default function TemplateEdit() {
    const { id } = useParams();

    const { data: templateData, isLoading } = useGraphQLQuery<TemplateViewResponse>(
        [QUERY_KEY.TEMPLATE, id],
        TEMPLATE_VIEW,
        { id: Number(id) },
        '',
        {
            enabled: !!id,
        }
    );

    const template = templateData?.template_view;

    if (!isLoading && !template) return <Navigate to={PATH.NOT_FOUND} />;

    return (
        <>
            <Helmet>
                <title>Edit Template</title>
            </Helmet>
            <ContentHeader
                title="Edit Template"
                breadcrumbs={[
                    {
                        text: 'Templates',
                        to: `/template/list/${template?.is_kit ? 'kit' : 'library'}`,
                    },
                    {
                        text: template ? `${template.name}` : 'Edit Template',
                        to: '',
                    },
                ]}
            />
            <div className="content-body">
                <div className="col-12">
                    {isLoading && <Spinner />}
                    {!isLoading && template && (
                        <UpdateTemplateForm
                            id={Number(id)}
                            type={template.is_kit ? 'kit' : 'library'}
                            template={template}
                        />
                    )}
                </div>
            </div>
        </>
    );
}
