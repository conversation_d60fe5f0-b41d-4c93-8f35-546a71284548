import { useTranslation } from 'react-i18next';
import { showToast } from 'utils/common';
import { useEffect, useState } from 'react';

interface IProps {
    id: number;
    label: string;
    onChangeFile: (file: File) => void;
    accept: string;
    fileNameInit?: string;
    fileUrl?: string;
    hideInput?: boolean;
}

export default function UploadFile({
    id,
    label,
    onChangeFile,
    accept,
    fileNameInit,
    fileUrl,
    hideInput,
}: Readonly<IProps>) {
    const [fileName, setFileName] = useState<string>();
    const [selectedFile, setSelectedFile] = useState<File>();

    const allowedExtensions = accept.split(',').map((ext) => ext.trim().replace('.', '').toLowerCase());

    const onChooseFile = (event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.files) {
            const file = event.target.files[0];
            const fileExt = file.name.split('.').pop()?.toLowerCase();
            if (fileExt && allowedExtensions.includes(fileExt)) {
                onChangeFile(file);
                setFileName(file.name);
                setSelectedFile(file);
            } else {
                showToast(false, ['Invalid file type. Allowed: ' + allowedExtensions.join(', ')]);
            }
        }
    };

    const handleDownload = () => {
        if (fileNameInit && fileUrl) {
            window.open(fileUrl, '_blank');
        } else if (selectedFile) {
            const url = URL.createObjectURL(selectedFile);
            const link = document.createElement('a');
            link.href = url;
            link.download = selectedFile.name;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }
    };

    useEffect(() => {
        if (fileNameInit) {
            setFileName(fileNameInit);
        }
    }, [fileNameInit]);

    return (
        <div className="d-flex">
            <div className="d-flex align-items-end mt-75">
                <div>
                    {!hideInput && (
                        <>
                            <label htmlFor={`upload-${id}`} className="btn btn-sm btn-primary mb-75 me-75">
                                {label}
                            </label>
                            <input onChange={onChooseFile} type="file" id={`upload-${id}`} hidden accept={accept} />
                        </>
                    )}
                    {fileName && (
                        <div
                            className="small"
                            onClick={fileName ? handleDownload : undefined}
                            style={{
                                cursor: fileName ? 'pointer' : 'default',
                                textDecoration: fileName ? 'underline' : 'none',
                            }}
                        >
                            {fileName}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}
