import { useState } from 'react';
import { Checkbox, FormControlLabel, FormGroup, Button } from '@mui/material';
import ModalContent from 'components/partials/ModalContent';
import ModalConfirm from 'components/partials/ModalConfirm';
import { PLUGIN_OPTIONS, SUCCESS_MESSAGE } from 'constants/common';
import { useGraphQLMutation } from 'hooks/useGraphQLMutation';
import { WEBSITES_WPCLI } from 'services/WebsiteService';
import { showToast } from 'utils/common';

interface ModalInstallPluginsProps {
    show: boolean;
    onClose: () => void;
    domain: string;
}

export default function ModalInstallPlugins({ show, onClose, domain }: ModalInstallPluginsProps) {
    const [selectedCommands, setSelectedCommands] = useState<string[]>([]);
    const [confirmOpen, setConfirmOpen] = useState(false);

    const { mutateAsync: websites_wpcli, status } = useGraphQLMutation(WEBSITES_WPCLI, 'websites_wpcli', {
        onSuccess: () => {
            showToast(true, [SUCCESS_MESSAGE]);
            onClose();
        },
    });

    const handleToggle = (command: string) => {
        setSelectedCommands((prev) =>
            prev.includes(command) ? prev.filter((c) => c !== command) : [...prev, command]
        );
    };

    const handleInstall = () => {
        if (selectedCommands.length === 0) return;
        setConfirmOpen(true);
    };

    const renderContent = () => (
        <div>
            <p className="mb-3 font-medium">
                Choose plugins to install for <strong>{domain}</strong>:
            </p>
            <FormGroup row className="gap-x-6 gap-y-2 flex-wrap">
                {PLUGIN_OPTIONS.map((plugin) => (
                    <FormControlLabel
                        key={plugin.command}
                        control={
                            <Checkbox
                                checked={selectedCommands.includes(plugin.command)}
                                onChange={() => handleToggle(plugin.command)}
                            />
                        }
                        label={plugin.label}
                    />
                ))}
            </FormGroup>

            <div className="text-end mt-4">
                <Button
                    variant="contained"
                    color="primary"
                    onClick={handleInstall}
                    disabled={selectedCommands.length === 0}
                >
                    Install
                </Button>
            </div>
        </div>
    );

    const isCreating = status === 'pending';

    return (
        <>
            <ModalContent show={show} changeShow={onClose} title="Install Plugins" content={renderContent()} />
            <ModalConfirm
                show={confirmOpen}
                text="Are you sure you want to install the selected plugins?"
                btnDisabled={isCreating}
                changeShow={() => setConfirmOpen(false)}
                submitAction={() =>
                    websites_wpcli({
                        input: {
                            domain: domain,
                            command: selectedCommands.join(' && '),
                        },
                    })
                }
            />
        </>
    );
}
