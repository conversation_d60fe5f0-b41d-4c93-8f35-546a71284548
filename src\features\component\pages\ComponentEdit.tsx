import ContentHeader from 'components/partials/ContentHeader';
import { Helmet } from 'react-helmet-async';
import { Navigate, useParams } from 'react-router-dom';
import UpdateComponentForm from '../components/UpdateComponentForm';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { PATH, QUERY_KEY } from '../../../constants/common';
import { TEMPLATE_COMPONENT_VIEW } from '../../../services/ComponentService';
import { ComponentViewResponse } from '../../../types/Component';
import Spinner from '../../../components/partials/Spinner';

export default function ComponentEdit() {
    const { id } = useParams();

    const { data: componentData, isLoading } = useGraphQLQuery<ComponentViewResponse>(
        [QUERY_KEY.COMPONENT, id],
        TEMPLATE_COMPONENT_VIEW,
        { id: Number(id) },
        '',
        {
            enabled: !!id,
        }
    );

    const component = componentData?.template_component_view;

    if (!isLoading && !component) return <Navigate to={PATH.NOT_FOUND} />;

    return (
        <>
            <Helmet>
                <title>Edit Elementor Component</title>
            </Helmet>
            <ContentHeader
                title="Edit Elementor Component"
                breadcrumbs={[
                    {
                        text: 'Elementor Components',
                        to: '/component',
                    },
                    {
                        text: component ? `${component.name}` : 'Edit Component',
                        to: '',
                    },
                ]}
            />
            <div className="content-body">
                <div className="col-12">
                    {isLoading && <Spinner />}
                    {!isLoading && component && <UpdateComponentForm id={Number(id)} component={component} />}
                </div>
            </div>
        </>
    );
}
