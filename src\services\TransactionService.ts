import { gql } from 'graphql-request';

export const TRANSACTION_LIST = gql`
    query Transactions_list($page: Int!, $limit: Int!, $filters: [String!], $sort: String, $search: String) {
        transactions_list(input: { page: $page, limit: $limit, filters: $filters, sort: $sort, search: $search }) {
            totalCount
            totalPages
            currentPage
            data {
                id
                customer_id
                plan_id
                type_id
                price
                status_id
                start_date
                end_date
                plan {
                    id
                    name
                }
                customer {
                    id
                    full_name
                    avatar {
                        file_url
                    }
                }
            }
        }
    }
`;

export const TRANSACTION_STATISTICS = gql`
    query Transactions_statistics($start_date: String!, $end_date: String, $type_id: Int!) {
        transactions_statistics(input: { start_date: $start_date, end_date: $end_date, type_id: $type_id }) {
            label
            paid
            trial
        }
    }
`;

export const TRANSACTIONS_TODAY_STATISTICS = gql`
    query Transactions_today_statistics {
        transactions_today_statistics {
            trial_transactions
            total_transactions
        }
    }
`;

export const CONFIGS_BY_CODE = gql`
    query Configs_by_code($code: String!) {
        configs_by_code(code: $code) {
            id
            code
            name
            value
        }
    }
`;
