import { gql } from 'graphql-request';

export const WORDPRESS_DEPLOY = gql`
    query Webhook_template_check_exits($template_id: Int!, $domain: String!) {
        webhook_template_check_exits(body: { template_id: $template_id, domain: $domain })
    }
`;

export const WORDPRESS_DELETE = gql`
    mutation Wordpress_delete($domain: String!) {
        wordpress_delete(body: { domain: $domain })
    }
`;

export const WORDPRESS_STATUS = gql`
    query Wordpress_status($domain: String!) {
        wordpress_status(domain: $domain)
    }
`;
