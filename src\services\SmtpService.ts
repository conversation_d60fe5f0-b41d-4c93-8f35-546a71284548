import { gql } from 'graphql-request';

export const SMTP_LIST = gql`
    query Smtps_list($page: Int!, $limit: Int!, $filters: [String!], $sort: String, $search: String) {
        smtps_list(input: { page: $page, limit: $limit, filters: $filters, sort: $sort, search: $search }) {
            totalCount
            totalPages
            currentPage
            data {
                id
                customer_id
                smtp_config
            }
        }
    }
`;

export const SMTP_CREATE = gql`
    mutation Smtps_create($customer_id: Float!, $smtp_config: SmtpConfigInputDto!) {
        smtps_create(input: { customer_id: $customer_id, smtp_config: $smtp_config }) {
            id
            customer_id
            smtp_config
        }
    }
`;

export const SMTP_UPDATE = gql`
    mutation Smtps_update($id: Int!, $input: SmtpSaveInputDto!) {
        smtps_update(id: $id, input: $input) {
            id
            customer_id
            smtp_config
        }
    }
`;

export const SMTP_DELETE = gql`
    mutation Smtps_delete($id: Int!) {
        smtps_delete(id: $id)
    }
`;
