import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import UpdateButton from 'components/partials/UpdateButton';
import UploadFile from 'components/partials/UploadFile';
import { ERROR_MESSAGE, SUCCESS_MESSAGE } from 'constants/common';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import FileService from 'services/FileService';
import { TEMPLATE_COMPONENT_CREATE, TEMPLATE_COMPONENT_UPDATE } from 'services/ComponentService';
import { ItemStatus, ItemStatusNames } from 'types/common/Item';
import { selectItem, showToast } from 'utils/common';
import * as yup from 'yup';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import Component from '../../../types/Component';
import { useTranslation } from 'react-i18next';

interface IProps {
    id: number;
    component?: Component;
}

export default function UpdateTemplateForm({ id, component }: Readonly<IProps>) {
    const { t } = useTranslation();
    const [file, setFile] = useState<File>();
    const navigate = useNavigate();

    const schema = yup
        .object({
            name: yup.string().required(t('error.required')).max(255).trim(),
        })
        .required();

    const {
        register,
        handleSubmit,
        formState: { errors, isSubmitting },
        reset,
    } = useForm<Component>({
        resolver: yupResolver(schema),
        defaultValues: {
            status_id: ItemStatus.ACTIVE,
        },
    });

    const templateCreateMutation = useGraphQLMutation<{}, { body: Partial<Component> }>(TEMPLATE_COMPONENT_CREATE, '', {
        onSuccess() {
            showToast(true, [SUCCESS_MESSAGE]);
            setTimeout(() => {
                navigate('/component');
            }, 2000);
        },
    });

    const templateUpdateMutation = useGraphQLMutation<{}, { id: number; body: Partial<Component> }>(
        TEMPLATE_COMPONENT_UPDATE,
        '',
        {
            onSuccess() {
                showToast(true, [SUCCESS_MESSAGE]);
                setTimeout(() => {
                    navigate('/component');
                }, 2000);
            },
        }
    );

    const handleUploadFile = async (file: File | undefined) => {
        if (!file) return null;
        const results = await FileService.upload(file);
        if (results.upload) {
            return results.upload.id;
        }
        showToast(false, [ERROR_MESSAGE]);
        return null;
    };

    const onSubmit = async (data: Component) => {
        let fileId = null;
        if (file) {
            fileId = (await handleUploadFile(file)) ?? 0;
        }
        if (!fileId && !data.file) {
            showToast(false, ['Please upload file']);
            return;
        }
        const formData = {
            ...data,
            file_id: fileId ?? data.file.id,
            id: undefined,
            file: undefined,
        };
        if (id > 0) {
            templateUpdateMutation.mutate({
                id,
                body: formData,
            });
        } else {
            templateCreateMutation.mutate({
                body: formData,
            });
        }
    };

    useEffect(() => {
        if (component) {
            reset(component);
        }
    }, [component, reset]);

    return (
        <div className="card">
            <div className="card-header border-bottom">
                <h4 className="card-title">{id > 0 ? 'Edit Elementor Component' : 'Add Elementor Component'}</h4>
            </div>
            <div className="card-body py-2 my-25">
                <form className="validate-form pt-50" onSubmit={handleSubmit(onSubmit)}>
                    <div className="row">
                        <div className="col-12 col-sm-6 mb-1">
                            <label className="form-label">
                                Name <span className="error">*</span>
                            </label>
                            <input
                                {...register('name')}
                                type="text"
                                className={classNames('form-control', {
                                    'is-invalid': Boolean(errors.name?.message),
                                })}
                            />
                            <span className="error">{errors.name?.message}</span>
                        </div>
                        <div className="col-12 col-sm-6 mb-1">
                            <label className="form-label">Status</label>
                            <select {...register('status_id', { valueAsNumber: true })} className="form-select">
                                {selectItem(ItemStatusNames, t, true)}
                            </select>
                        </div>

                        <div className="col-12 mb-1">
                            <label className="form-label">Description</label>
                            <textarea {...register('desc')} className="form-control" rows={2} />
                        </div>
                        <div className="col-12 ">
                            <div className="card" style={{ background: '#f8f9fa', border: 'none' }}>
                                <div className="card-body pb-2 pt-2">
                                    <h6 className="mb-1" style={{ fontWeight: 600 }}>
                                        Additional Files <span className="error">*</span>
                                    </h6>
                                    <div className="row">
                                        <div className="col-6">
                                            <UploadFile
                                                fileNameInit={component?.file?.file_name}
                                                fileUrl={component?.file?.file_url}
                                                id={4}
                                                label="Upload file"
                                                onChangeFile={setFile}
                                                accept=".zip"
                                            />
                                            <small className="text-muted">Allowed: .zip</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="col-12 mt-2">
                            <UpdateButton btnText={t('update')} isLoading={isSubmitting} btnClass={['mt-1']} />
                        </div>
                    </div>
                </form>
            </div>
        </div>
    );
}
