import ContentHeader from 'components/partials/ContentHeader';
import ModalConfirm from 'components/partials/ModalConfirm';
import Spinner from 'components/partials/Spinner';
import { LIMIT_DEFAULT, PAGE_NUMBER_DEFAULT, QUERY_KEY, SUCCESS_MESSAGE } from 'constants/common';
import { find } from 'lodash';
import { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { ACTION_LIST, ACTION_CREATE, ACTION_UPDATE, ACTION_DELETE, getFlatActions } from 'services/ActionService';
import Action, { ActionQuery } from 'types/Action';
import { showToast } from 'utils/common';
import ListAction from '../components/ListAction';
import ModalActionUpdate from '../components/ModalActionUpdate';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import { BaseSearch } from '../../../types/common';

export default function ActionList() {
    const [itemId, setItemId] = useState(0);
    const [showDelete, setShowDelete] = useState(false);
    const [showUpdate, setShowUpdate] = useState(false);
    const { t } = useTranslation();

    const {
        data: actionData,
        isLoading,
        isRefetching,
        refetch,
    } = useGraphQLQuery<ActionQuery, BaseSearch>([QUERY_KEY.ACTIONS], ACTION_LIST, {
        limit: LIMIT_DEFAULT,
        page: PAGE_NUMBER_DEFAULT,
        sort: 'display_order:ASC',
    });
    const actions = getFlatActions(actionData?.actions_list.data ?? []);

    const updateMutation = useGraphQLMutation<{}, Partial<Action>>(itemId ? ACTION_UPDATE : ACTION_CREATE, '', {
        onSuccess: () => {
            showToast(true, [SUCCESS_MESSAGE]);
            setShowUpdate(false);
            setItemId(0);
            refetch();
        },
    });

    const deleteMutation = useGraphQLMutation(ACTION_DELETE, '', {
        onSuccess: () => {
            showToast(true, [SUCCESS_MESSAGE]);
            setShowDelete(false);
            setItemId(0);
            refetch();
        },
    });

    const updateItem = async (data: Action) => {
        if (!data.parent_id) delete data.parent_id;
        data.parent_id = Number(data.parent_id);
        updateMutation.mutate(data);
    };

    const deleteItem = async () => {
        if (itemId) {
            deleteMutation.mutate({ id: Number(itemId) });
        }
    };

    const handleEdit = (id: number) => {
        updateMutation.reset();
        setItemId(id);
        setShowUpdate(true);
    };

    const handleDelete = (id: number) => {
        deleteMutation.reset();
        setItemId(id);
        setShowDelete(true);
    };

    const actionMenu = (_id: number) => {
        updateMutation.reset();
        setShowUpdate(true);
        setItemId(0);
    };

    return (
        <>
            <Helmet>
                <title>{t('actions')}</title>
            </Helmet>
            <ContentHeader
                title={t('actions')}
                contextMenu={[
                    {
                        text: t('add'),
                        to: '',
                        icon: 'PLUS',
                        fnCallBack: { actionMenu },
                    },
                ]}
            />
            <div className="content-body">
                <div className="col-12">
                    {(isLoading || isRefetching) && <Spinner />}
                    {!isLoading && !isRefetching && (
                        <>
                            <div className="card">
                                <ListAction items={actions} handleEdit={handleEdit} handleDelete={handleDelete} />
                            </div>
                            <ModalActionUpdate
                                show={showUpdate}
                                action={find(actions, { id: itemId })}
                                listActions={actions}
                                isLoading={updateMutation.isPending}
                                changeShow={(s: boolean) => setShowUpdate(s)}
                                submitAction={updateItem}
                            />
                        </>
                    )}
                    <ModalConfirm
                        show={showDelete}
                        text={t('confirm.delete', { data: 'action' })}
                        btnDisabled={deleteMutation.isPending}
                        changeShow={(s: boolean) => setShowDelete(s)}
                        submitAction={deleteItem}
                    />
                </div>
            </div>
        </>
    );
}
