import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import { BACKUP_WEBSITES_LIST } from 'services/WebsiteStyleService';
import { useEffect, useMemo, useState } from 'react';
import ModalContent from 'components/partials/ModalContent';
import Spinner from 'components/partials/Spinner';
import { QUERY_KEY } from 'constants/common';
import { WebsiteBackup, WebsiteBackupQuery } from 'types/Website';
import { FORMAT_DATE, formatDateTime } from 'utils/date';
import { RefreshCcw } from 'react-feather';
import ModalRestore from './ModalRestore';
import ModalCreateBackup from './ModalCreateBackup';

interface ModalBackupListProps {
    show: boolean;
    onClose: () => void;
    websiteId: number;
}

export default function ModalBackupList({ show, onClose, websiteId }: ModalBackupListProps) {
    const [backups, setBackups] = useState<WebsiteBackup[]>([]);
    const [selectedRestoreId, setSelectedRestoreId] = useState<number>();
    const [showModalRestore, setShowModalRestore] = useState(false);
    const [createModalOpen, setCreateModalOpen] = useState(false);

    const {
        data,
        refetch,
        isLoading: isFetching,
    } = useGraphQLQuery<WebsiteBackupQuery>([QUERY_KEY.BACKUP_WEBSITES_LIST, websiteId], BACKUP_WEBSITES_LIST, {
        input: {
            page: 1,
            limit: 999,
            filters: [`website_id:=(${websiteId})`],
        },
    });

    useEffect(() => {
        if (data?.backup_websites_list?.data) {
            setBackups(data.backup_websites_list.data);
        }
    }, [data]);

    const handleRestore = (id: number) => {
        setSelectedRestoreId(id);
        setShowModalRestore(true);
    };

    const handleCreateBackup = () => {
        setCreateModalOpen(true);
    };

    const rows = useMemo(
        () =>
            backups.map((item, index) => (
                <tr key={item.id}>
                    <td>{index + 1}</td>
                    <td>
                        {item.file?.file_url ? (
                            <a href={item.file.file_url} target="_blank" rel="noopener noreferrer">
                                {item.file.file_name}
                            </a>
                        ) : (
                            '-'
                        )}
                    </td>
                    <td>{formatDateTime(item.created_at!, FORMAT_DATE.SHOW_DATE_MINUTE)}</td>
                    <td className="text-center">
                        {item.file?.file_url && (
                            <button
                                className="inline-flex items-center justify-center text-blue-500 hover:text-blue-700 transition border-none outline-none focus:outline-none"
                                onClick={() => handleRestore(item.id!)}
                                title="Restore"
                            >
                                <RefreshCcw size={18} />
                            </button>
                        )}
                    </td>
                </tr>
            )),
        [backups]
    );

    const renderContent = () => (
        <div>
            <table className="table table-bordered">
                <caption className="caption-top text-start mb-2">
                    <button onClick={handleCreateBackup} className="btn btn-primary">
                        Create Backup
                    </button>
                </caption>
                <thead>
                    <tr>
                        <th>No</th>
                        <th>File Backup</th>
                        <th>Created Time</th>
                        <th className="text-center">Restore</th>
                    </tr>
                </thead>
                <tbody>
                    {isFetching ? (
                        <tr>
                            <td colSpan={4}>
                                <Spinner />
                            </td>
                        </tr>
                    ) : backups.length === 0 ? (
                        <tr>
                            <td colSpan={4} className="text-center text-muted">
                                No backups found.
                            </td>
                        </tr>
                    ) : (
                        rows
                    )}
                </tbody>
            </table>

            {selectedRestoreId && (
                <ModalRestore
                    show={showModalRestore}
                    onClose={() => setShowModalRestore(false)}
                    websiteId={selectedRestoreId}
                    onBackupSuccess={refetch}
                />
            )}

            {createModalOpen && (
                <ModalCreateBackup
                    show={createModalOpen}
                    onClose={() => setCreateModalOpen(false)}
                    websiteId={websiteId}
                    onBackupSuccess={() => {
                        refetch();
                        setCreateModalOpen(false);
                    }}
                />
            )}
        </div>
    );

    return <ModalContent show={show} changeShow={onClose} title="Backup List" content={renderContent()} />;
}
