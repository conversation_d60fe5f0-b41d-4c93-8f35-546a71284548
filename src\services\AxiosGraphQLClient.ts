import axios, { AxiosError, AxiosInstance, HttpStatusCode } from 'axios';
import { Variables } from 'graphql-request';
import { AUTH_KEYS } from 'constants/auth';
import { ERROR_MESSAGE, OPERATION_NAME, REACT_APP_API_URL } from 'constants/common';
import { clearStorage, getLocalStorage, removeLocalStorage, setLocalStorage } from 'utils/localStorage';
import includes from 'lodash/includes';
import { RefreshQuery, UserQuery } from '../types/User';
import { REFRESH } from './UserService';
import { showToast } from '../utils/common';

export interface GraphQLErrorResponse {
    errors: {
        message: string;
        code: number;
        errors: {
            property: string;
            constraints: {
                [key: string]: string;
            };
        }[];
    }[];
    data: null;
}

interface GraphQLRequest {
    query: string;
    variables?: Variables;
    operationName?: string;
}

export default class AxiosGraphQLClient {
    private client: AxiosInstance;
    private refreshToken: string;
    private accessToken: string;
    private readonly endpoint: string;

    constructor() {
        this.endpoint = `${REACT_APP_API_URL}/graphql`;
        this.accessToken = getLocalStorage(AUTH_KEYS.ACCESS_TOKEN);
        this.refreshToken = getLocalStorage(AUTH_KEYS.REFRESH_TOKEN);
        this.client = this.createClient();
    }

    private createClient(): AxiosInstance {
        return axios.create({
            baseURL: this.endpoint,
            headers: this.getHeaders(),
        });
    }

    private getHeaders(): Record<string, string> {
        const headers: Record<string, string> = {
            'Content-Type': 'application/json',
        };

        if (this.accessToken) {
            headers['Authorization'] = `Bearer ${this.accessToken}`;
        }

        return headers;
    }

    private updateHeaders(): void {
        this.client.defaults.headers.common = {
            ...this.client.defaults.headers.common,
            ...this.getHeaders(),
        };
    }

    private extractOperationName(query: string): string {
        return (
            query.match(/(?:query|mutation)\s+([A-Za-z0-9_]+)/)?.[1] ?? query.match(/{\s*([A-Za-z0-9_]+)/)?.[1] ?? ''
        );
    }

    private async validateToken(operationName: string): Promise<void> {
        const publicOperations = ['login', 'register', 'forgotPassword', 'changePassword', 'resetPassword'];

        if (includes(publicOperations, operationName)) {
            return;
        }

        this.updateHeaders();
    }

    // tslint:disable-next-line: no-any
    public async request<T = any, V extends Variables = Variables>(
        query: string,
        variables?: V,
        operationName?: string,
        isShowToast = true,
        customHeaders?: Record<string, string>
    ): Promise<T> {
        const effectiveOperationName = this.extractOperationName(query);

        await this.validateToken(effectiveOperationName);

        if (operationName === OPERATION_NAME.LOGOUT) {
            this.accessToken = '';
            this.refreshToken = '';
            removeLocalStorage(AUTH_KEYS.ACCESS_TOKEN);
            removeLocalStorage(AUTH_KEYS.REFRESH_TOKEN);
        }

        try {
            const graphqlRequest: GraphQLRequest = {
                query,
                variables,
                operationName: effectiveOperationName || undefined,
            };

            // Create a temporary config with custom headers if provided
            const requestConfig = customHeaders
                ? {
                      headers: {
                          ...this.client.defaults.headers.common,
                          ...customHeaders,
                      },
                  }
                : {};

            const response = await this.client.post<{ data: T }>('', graphqlRequest, requestConfig);

            if (operationName === OPERATION_NAME.LOGIN) {
                const dataLogin = response.data.data as unknown as UserQuery;
                this.accessToken = dataLogin.auth_login.token.access_token;
                this.refreshToken = dataLogin.auth_login.token.refresh_token;
                setLocalStorage(AUTH_KEYS.ACCESS_TOKEN, this.accessToken);
                setLocalStorage(AUTH_KEYS.REFRESH_TOKEN, this.refreshToken);
            }

            return response.data.data;
            // tslint:disable-next-line: no-any
        } catch (error: any) {
            if (operationName === OPERATION_NAME.LOGOUT) {
                return {} as T;
            }

            if (error instanceof AxiosError) {
                const errorData: GraphQLErrorResponse = error?.response?.data;
                let errorMsg = ERROR_MESSAGE;
                if (
                    error.status === HttpStatusCode.Unauthorized &&
                    operationName !== OPERATION_NAME.LOGIN &&
                    this.refreshToken
                ) {
                    try {
                        await this.handleRefreshToken();
                        return this.request<T>(query, variables, operationName, isShowToast, customHeaders);
                    } catch (refreshError) {
                        this.handleErrorRefreshToken();
                        errorMsg = 'Session expired. Please login again.';
                        if (isShowToast) {
                            showToast(false, [errorMsg]);
                        }
                        // Return error data from API server if available, otherwise create error structure
                        throw (
                            errorData || {
                                errors: [
                                    {
                                        message: errorMsg,
                                        code: HttpStatusCode.Unauthorized,
                                        errors: [],
                                    },
                                ],
                                data: null,
                            }
                        );
                    }
                } else if (error.status === HttpStatusCode.BadRequest) {
                    if (errorData.errors[0].errors) {
                        const listErrs: string[] = [];
                        errorData.errors[0].errors.forEach((item) => {
                            Object.keys(item.constraints).forEach((key) => {
                                //listErrs.push(`${item.property}: ${item.constraints[key]}`);
                                listErrs.push(item.constraints[key]);
                            });
                        });
                        if (isShowToast) {
                            showToast(false, listErrs);
                        }
                    } else {
                        if (isShowToast) {
                            showToast(false, [errorData.errors[0].message]);
                        }
                    }
                    throw errorData;
                } else if (error.status === HttpStatusCode.NotFound) {
                    // errorMsg = 'Data not found.';
                    // if (isShowToast) {
                    //     showToast(false, [errorMsg]);
                    // }
                    // Return error data from API server if available, otherwise create error structure
                    throw (
                        errorData || {
                            errors: [
                                {
                                    message: errorMsg,
                                    code: HttpStatusCode.NotFound,
                                    errors: [],
                                },
                            ],
                            data: null,
                        }
                    );
                } else {
                    if (isShowToast) {
                        showToast(false, [ERROR_MESSAGE]);
                    }
                    // Return error data from API server if available, otherwise create error structure
                    throw (
                        errorData || {
                            errors: [
                                {
                                    message: errorMsg,
                                    code: error.status || 500,
                                    errors: [],
                                },
                            ],
                            data: null,
                        }
                    );
                }
            }

            // Handle non-AxiosError cases
            const genericErrorMessage = error?.message || ERROR_MESSAGE;
            if (isShowToast) {
                showToast(false, [genericErrorMessage]);
            }

            // Return consistent error structure
            throw {
                errors: [
                    {
                        message: genericErrorMessage,
                        code: 500,
                        errors: [],
                    },
                ],
                data: null,
            } as GraphQLErrorResponse;
        }
    }

    private handleErrorRefreshToken(): void {
        this.accessToken = '';
        this.refreshToken = '';
        clearStorage();
    }

    private async handleRefreshToken(): Promise<void> {
        try {
            const graphqlRequest: GraphQLRequest = {
                query: REFRESH,
                variables: { refresh_token: this.refreshToken },
            };

            const response = await this.client.post<{ data: RefreshQuery }>('', graphqlRequest);

            if (response.data.data.auth_refresh) {
                this.accessToken = response.data.data.auth_refresh.token.access_token;
                this.refreshToken = response.data.data.auth_refresh.token.refresh_token;
                setLocalStorage(AUTH_KEYS.ACCESS_TOKEN, this.accessToken);
                setLocalStorage(AUTH_KEYS.REFRESH_TOKEN, this.refreshToken);
                this.updateHeaders();
                this.client = this.createClient();
                return;
            }

            this.handleErrorRefreshToken();
        } catch (error) {
            // tslint:disable-next-line: no-console
            console.error(error);
            this.handleErrorRefreshToken();
        }
    }
}

const axiosGraphQLClient = new AxiosGraphQLClient();
export { axiosGraphQLClient };
