import { gql } from 'graphql-request';

export const WEBSITE_STYLE_LIST = gql`
    query Website_style_list($filters: [String!], $sort: String, $search: String) {
        website_style_list(body: { filters: $filters, sort: $sort, search: $search }) {
            id
            name
            desc
            display_order
            status_id
        }
    }
`;

export const WEBSITE_STYLE_CREATE = gql`
    mutation Website_style_create($name: String!, $desc: String!, $display_order: Int!, $status_id: Int!) {
        website_style_create(body: { name: $name, desc: $desc, display_order: $display_order, status_id: $status_id }) {
            id
        }
    }
`;

export const WEBSITE_STYLE_UPDATE = gql`
    mutation Website_style_update($id: Int!, $name: String!, $desc: String!, $display_order: Int!, $status_id: Int!) {
        website_style_update(
            id: $id
            body: { name: $name, desc: $desc, display_order: $display_order, status_id: $status_id }
        ) {
            id
        }
    }
`;

export const WEBSITE_STYLE_DELETE = gql`
    mutation Website_style_delete($id: Int!) {
        website_style_delete(id: $id)
    }
`;

export const BACKUP_WEBSITES_LIST = gql`
    query Backup_websites_list($input: BasePaginationInput!) {
        backup_websites_list(input: $input) {
            totalCount
            totalPages
            currentPage
            data {
                id
                created_by
                updated_by
                created_at
                updated_at
                website_id
                file_id
                file {
                    file_name
                    file_url
                }
                is_request
            }
        }
    }
`;

export const MUTATION_REQUEST_BACKUP = gql`
    mutation Websites_request_backup($id: Int!) {
        websites_request_backup(id: $id) {
            id
            website_id
        }
    }
`;
