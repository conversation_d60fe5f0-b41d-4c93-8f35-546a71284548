import { SelectOptionModel } from 'types/common/Item';

export type FieldType = 'text' | 'select' | 'date' | 'number' | 'hidden';

export interface SearchFieldBase {
    name: string;
    type: FieldType;
    label?: string;
    wrapClassName?: string;
    show?: boolean;
    value?: string | number;
    placeholder?: string;
}

export interface TextField extends SearchFieldBase {
    type: 'text';
}

export interface SelectField extends SearchFieldBase {
    type: 'select';
    options: {
        multiple: boolean;
        choices: SelectOptionModel[];
    };
}

export interface DateField extends SearchFieldBase {
    type: 'date';
}

export interface NumberField extends SearchFieldBase {
    type: 'number';
}

export interface HiddenField extends SearchFieldBase {
    type: 'hidden';
}

export type SearchField = TextField | SelectField | DateField | NumberField | HiddenField;
