import { ItemFileUpload } from 'types/common/Item';
import Http from './http.class';

const http = new Http('multipart/form-data').instance;

const FileService = {
    async upload(fileObj: File, type: string = '') {
        const formData = new FormData();
        formData.append('file', fileObj);
        const { data } = await http.post<{ upload: ItemFileUpload }>(
            type === '' ? '/files/upload' : `/files/upload?type=${type}`,
            formData
        );
        return data;
    },
};

export default FileService;
