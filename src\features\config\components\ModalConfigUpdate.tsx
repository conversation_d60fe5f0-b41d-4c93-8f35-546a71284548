// components/ModalConfigUpdate.tsx

import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import UpdateButton from 'components/partials/UpdateButton';
import { useEffect, useLayoutEffect } from 'react';
import { useForm } from 'react-hook-form';
import { toggleModalOpen } from 'utils/common';
import * as yup from 'yup';
import { useTranslation } from 'react-i18next';
import Config from 'types/Config';

interface IProps {
    show: boolean;
    config?: Config;
    isLoading?: boolean;
    changeShow: (s: boolean) => void;
    submitAction: (value: string) => void;
}

interface ConfigForm {
    value: string;
}

export default function ModalConfigUpdate({ show, config, isLoading, changeShow, submitAction }: Readonly<IProps>) {
    const { t } = useTranslation();

    useLayoutEffect(() => toggleModalOpen(show), [show]);

    const schema = yup.object({
        value: yup.string().required(t('error.required')).trim(),
    });

    const {
        register,
        handleSubmit,
        reset,
        formState: { errors },
    } = useForm<ConfigForm>({
        resolver: yupResolver(schema),
    });

    useEffect(() => {
        if (config && show) {
            reset({ value: config.value });
        }
    }, [config, show, reset]);

    return (
        <>
            <div
                className={classNames('modal fade text-start modal-primary', { show })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-dialog-centered">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">Update Configs</h5>
                            <button type="button" className="btn-close" onClick={() => changeShow(false)} />
                        </div>
                        <form
                            className="validate-form pt-50"
                            onSubmit={handleSubmit((data) => submitAction(data.value))}
                        >
                            <div className="modal-body">
                                <div className="mb-1">
                                    <label className="form-label">Name</label>
                                    <input
                                        type="text"
                                        className="form-control"
                                        value={config?.name || ''}
                                        disabled
                                        readOnly
                                    />
                                </div>
                                <div className="mb-1">
                                    <label className="form-label">Code</label>
                                    <input
                                        type="text"
                                        className="form-control"
                                        value={config?.code || ''}
                                        disabled
                                        readOnly
                                    />
                                </div>
                                <div className="mb-1">
                                    <label className="form-label">
                                        Value <span className="error">*</span>
                                    </label>
                                    <input
                                        {...register('value')}
                                        type="text"
                                        className={classNames('form-control', {
                                            'is-invalid': Boolean(errors.value?.message),
                                        })}
                                    />
                                    <span className="error">{errors.value?.message}</span>
                                </div>
                            </div>
                            <div className="modal-footer">
                                <UpdateButton btnText={t('update')} isLoading={isLoading} hasDivWrap={false} />
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}
        </>
    );
}
