import ContentHeader from 'components/partials/ContentHeader';
import Spinner from 'components/partials/Spinner';
import { PAGINATION, QUERY_KEY, SUCCESS_MESSAGE } from 'constants/common';
import { useGraphQLMutation } from 'hooks/useGraphQLMutation';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { TICKET_LIST, TICKET_UPDATE } from 'services/TicketService';
import Ticket, {
    SearchTicketParam,
    ticketFilterConfig,
    TicketQuery,
    TicketStatusNames,
    TicketTypeNames,
} from 'types/Ticket';
import { convertConstantToSelectOptions, convertSelectToStringKey, generateFilters, showToast } from 'utils/common';
import { useTranslation } from 'react-i18next';
import ListTicket from '../components/ListTicket';
import { keepPreviousData } from '@tanstack/react-query';
import isUndefined from 'lodash/isUndefined';
import { omitBy } from 'lodash';
import useQueryParams from 'hooks/useQueryParams';
import PaginationTable from 'components/partials/PaginationTable';
import SearchForm from 'components/partials/SearchForm';
import ModalTicketUpdate from '../components/ModalTicketUpdate';

export default function MasterTagList() {
    const { t } = useTranslation();
    const [showModal, setShowModal] = useState(false);
    const [selectedTicket, setSelectedTicket] = useState<Ticket | undefined>(undefined);

    const { queryParams, setQueryParams } = useQueryParams<SearchTicketParam>();
    const paramConfig: SearchTicketParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            search: queryParams.search,
            status_id: queryParams.status_id,
            type_id: queryParams.type_id,
        },
        isUndefined
    );
    const { search, limit, page, ...dataParamConfig } = paramConfig;
    const filters = generateFilters(dataParamConfig, ticketFilterConfig);

    const { data, isLoading, isRefetching, refetch } = useGraphQLQuery<TicketQuery>(
        [QUERY_KEY.TICKETS, paramConfig, filters],
        TICKET_LIST,
        {
            page: Number(page),
            limit: limit,
            search,
            filters: filters.length > 0 ? filters : undefined,
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    const updateMutation = useGraphQLMutation<{}>(TICKET_UPDATE, '', {
        onSuccess: () => {
            setShowModal(false);
            showToast(true, [SUCCESS_MESSAGE]);
            refetch();
        },
    });

    const handleEdit = (id: number) => {
        const ticket = data?.tickets_list.data.find((item) => item.id === id);
        setSelectedTicket(ticket);
        setShowModal(true);
    };

    const handleSubmit = (formData: Ticket) => {
        if (selectedTicket?.id) {
            updateMutation.mutate({
                id: selectedTicket.id,
                input: {
                    feedback: formData.feedback,
                    status_id: formData.status_id,
                },
            });
        }
    };

    return (
        <>
            <Helmet>
                <title>Ticket</title>
            </Helmet>
            <ContentHeader title="Ticket" />
            <div className="content-body">
                <div className="col-12">
                    <SearchForm
                        fields={[
                            { name: 'search', type: 'text', label: t('keywords'), wrapClassName: 'col-md-4 col-12' },
                            {
                                name: 'type_id',
                                type: 'select',
                                label: t('typeName'),
                                wrapClassName: 'col-md-4 col-12',
                                options: {
                                    multiple: true,
                                    choices: convertSelectToStringKey(
                                        convertConstantToSelectOptions(TicketTypeNames, t, true)
                                    ),
                                },
                            },
                            {
                                name: 'status_id',
                                type: 'select',
                                label: t('statusName'),
                                wrapClassName: 'col-md-4 col-12',
                                options: {
                                    multiple: true,
                                    choices: convertSelectToStringKey(
                                        convertConstantToSelectOptions(TicketStatusNames, t, true)
                                    ),
                                },
                            },
                        ]}
                        isLoading={isLoading || isRefetching}
                    />
                    {(isLoading || isRefetching) && <Spinner />}
                    {!isLoading && !isRefetching && (
                        <div className="card">
                            <ListTicket items={data?.tickets_list.data ?? []} handleEdit={handleEdit} />
                            <PaginationTable
                                countItem={data?.tickets_list.totalCount}
                                totalPage={data?.tickets_list.totalPages}
                                currentPage={data?.tickets_list.currentPage}
                                handlePageChange={handlePageChange}
                            />
                        </div>
                    )}
                    <ModalTicketUpdate
                        show={showModal}
                        ticket={selectedTicket}
                        changeShow={(s: boolean) => setShowModal(s)}
                        submitAction={handleSubmit}
                    />
                </div>
            </div>
        </>
    );
}
