import Box from '@mui/material/Box';
import ContentHeader from 'components/partials/ContentHeader';
import Spinner from 'components/partials/Spinner';
import { PATH, QUERY_KEY } from 'constants/common';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { Navigate, useParams } from 'react-router-dom';
import UpdateSubscriptionPlanForm from '../components/UpdateSubscriptionPlanForm';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { SubscriptionPlanDetail } from 'types/SubcriptionPlan';
import { SUBSCRIPTION_PLAN_DETAIL } from 'services/SubscriptionPlanService';

export default function SubscriptionPlanEdit() {
    const { id } = useParams();
    const { t } = useTranslation();

    const { data: subscriptionPlanData, isLoading } = useGraphQLQuery<SubscriptionPlanDetail, { id: number }>(
        [QUERY_KEY.SUBSCRIPTION_PLAN, id],
        SUBSCRIPTION_PLAN_DETAIL,
        { id: Number(id) },
        '',
        {
            enabled: !!id,
        }
    );

    const subscriptionPlan = subscriptionPlanData?.subscription_plan_view;

    if (!isLoading && !subscriptionPlan) return <Navigate to={PATH.NOT_FOUND} />;

    return (
        <>
            <Helmet>
                <title>{t('subscription_plan.edit')}</title>
            </Helmet>
            <ContentHeader
                title={t('subscription_plan.edit')}
                breadcrumbs={[
                    {
                        text: t('subscription_plan.single'),
                        to: `/subscriptionPlan`,
                    },
                    {
                        text: subscriptionPlan ? `${subscriptionPlan.name}` : t('subscription_plan.edit'),
                        to: '',
                    },
                ]}
            />
            <div className="content-body">
                {isLoading && <Spinner />}
                {!isLoading && subscriptionPlan && (
                    <Box sx={{ width: '100%', typography: 'body1' }}>
                        <UpdateSubscriptionPlanForm id={id ?? ''} subscriptionPlan={subscriptionPlan} />
                    </Box>
                )}
            </div>
        </>
    );
}
