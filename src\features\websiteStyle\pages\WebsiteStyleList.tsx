import ContentHeader from 'components/partials/ContentHeader';
import ModalConfirm from 'components/partials/ModalConfirm';
import Spinner from 'components/partials/Spinner';
import { LIMIT_DEFAULT, PAGE_NUMBER_DEFAULT, QUERY_KEY, SUCCESS_MESSAGE } from 'constants/common';
import { useGraphQLMutation } from 'hooks/useGraphQLMutation';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import { useState } from 'react';
import { Helmet } from 'react-helmet-async';

import { useAuthStore } from 'stores/authStore';
import { showToast } from 'utils/common';
import ListWebsiteStyle from '../components/ListWebsiteStyle';
import ModalWebsiteStyleUpdate from '../components/ModalWebsiteStyleUpdate';
import {
    WEBSITE_STYLE_CREATE,
    WEBSITE_STYLE_DELETE,
    WEBSITE_STYLE_LIST,
    WEBSITE_STYLE_UPDATE,
} from 'services/WebsiteStyleService';
import WebsiteStyle, { WebsiteStyleQuery } from 'types/WebsiteStyle';
import { useTranslation } from 'react-i18next';

export default function WebsiteStyleList() {
    const { user } = useAuthStore();
    const { t } = useTranslation();
    const [showDelete, setShowDelete] = useState(false);
    const [showModal, setShowModal] = useState(false);
    const [selectedWebsiteStyle, setSelectedWebsiteStyle] = useState<WebsiteStyle | undefined>(undefined);
    const [itemId, setItemId] = useState(0);

    const { data, isLoading, isRefetching, refetch } = useGraphQLQuery<WebsiteStyleQuery>(
        [QUERY_KEY.WEBSITE_STYLES],
        WEBSITE_STYLE_LIST,
        {
            sort: 'display_order:ASC',
        },
        '',
        {
            enabled: !!user,
        }
    );

    const deleteMutation = useGraphQLMutation(WEBSITE_STYLE_DELETE, '', {
        onSuccess: () => {
            showToast(true, [SUCCESS_MESSAGE]);
            setShowDelete(false);
            setItemId(0);
            refetch();
        },
    });

    const createMutation = useGraphQLMutation<{}>(WEBSITE_STYLE_CREATE, '', {
        onSuccess: () => {
            showToast(true, [SUCCESS_MESSAGE]);
            setShowModal(false);
            refetch();
        },
    });

    const updateMutation = useGraphQLMutation<{}>(WEBSITE_STYLE_UPDATE, '', {
        onSuccess: () => {
            showToast(true, [SUCCESS_MESSAGE]);
            setShowModal(false);
            refetch();
        },
    });

    const handleDelete = (id: number) => {
        setItemId(id);
        setShowDelete(true);
    };

    const deleteItem = () => {
        deleteMutation.mutate({ id: itemId });
    };

    const handleAdd = () => {
        setSelectedWebsiteStyle(undefined);
        setShowModal(true);
    };

    const handleEdit = (id: number) => {
        const websiteStyle = data?.website_style_list.find((item) => item.id === id);
        setSelectedWebsiteStyle(websiteStyle);
        setShowModal(true);
    };

    const handleSubmit = (formData: WebsiteStyle) => {
        if (selectedWebsiteStyle?.id) {
            updateMutation.mutate({
                id: selectedWebsiteStyle.id,
                name: formData.name,
                desc: formData.desc,
                display_order: formData.display_order,
                status_id: formData.status_id,
            });
        } else {
            createMutation.mutate({
                name: formData.name,
                display_order: formData.display_order,
                status_id: formData.status_id,
                desc: formData.desc,
            });
        }
    };

    return (
        <>
            <Helmet>
                <title>Website Styles</title>
            </Helmet>
            <ContentHeader
                title="Website Styles"
                contextMenu={[
                    {
                        text: 'Add Website Style',
                        icon: 'PLUS',
                        fnCallBack: { actionMenu: () => handleAdd() },
                    },
                ]}
            />
            <div className="content-body">
                <div className="col-12">
                    {(isLoading || isRefetching) && <Spinner />}
                    {!isLoading && !isRefetching && (
                        <div className="card">
                            <ListWebsiteStyle
                                items={data?.website_style_list ?? []}
                                handleDelete={handleDelete}
                                handleEdit={handleEdit}
                            />
                        </div>
                    )}
                    <ModalConfirm
                        show={showDelete}
                        text={t('confirm.delete', { data: 'website style' })}
                        btnDisabled={deleteMutation.isPending}
                        changeShow={(s: boolean) => setShowDelete(s)}
                        submitAction={deleteItem}
                    />
                    <ModalWebsiteStyleUpdate
                        show={showModal}
                        websiteStyle={selectedWebsiteStyle}
                        isLoading={createMutation.isPending || updateMutation.isPending}
                        changeShow={(s: boolean) => setShowModal(s)}
                        submitAction={handleSubmit}
                    />
                </div>
            </div>
        </>
    );
}
