import { keepPreviousData } from '@tanstack/react-query';
import ContentHeader from 'components/partials/ContentHeader';
import ModalConfirm from 'components/partials/ModalConfirm';
import Spinner from 'components/partials/Spinner';
import { PAGINATION, QUERY_KEY, SUCCESS_MESSAGE } from 'constants/common';
import useQueryParams from 'hooks/useQueryParams';
import isUndefined from 'lodash/isUndefined';
import omitBy from 'lodash/omitBy';
import { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import Component, { SearchComponentParam, componentFilterConfig, ComponentQuery } from 'types/Component';
import { convertPaging, generateFilters, showToast } from 'utils/common';
import SearchComponentForm from '../../component/components/SearchComponentForm';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import { TEMPLATE_COMPONENT_DELETE, TEMPLATE_COMPONENT_LIST } from 'services/ComponentService';
import ListComponent from '../../component/components/ListComponent';
import PaginationTable from '../../../components/partials/PaginationTable';

export default function ComponentList() {
    const { t } = useTranslation();

    const { queryParams, setQueryParams } = useQueryParams<SearchComponentParam>();
    const paramConfig: SearchComponentParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            search: queryParams.search,
            status_id: queryParams.status_id,
        },
        isUndefined
    );
    const { search, limit, page, ...dataParamConfig } = paramConfig;
    const filters = generateFilters(dataParamConfig, componentFilterConfig);

    const [showDelete, setShowDelete] = useState(false);
    const [itemId, setItemId] = useState(0);

    const { data, isLoading, isRefetching, refetch } = useGraphQLQuery<ComponentQuery>(
        [QUERY_KEY.COMPONENTS, paramConfig, filters],
        TEMPLATE_COMPONENT_LIST,
        {
            page: Number(page),
            limit: limit,
            search,
            filters: filters.length > 0 ? filters : undefined,
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );

    const deleteMutation = useGraphQLMutation(TEMPLATE_COMPONENT_DELETE, '', {
        onSuccess: () => {
            showToast(true, [SUCCESS_MESSAGE]);
            setShowDelete(false);
            setItemId(0);
            refetch();
        },
    });

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    const handleDelete = (id: number) => {
        deleteMutation.reset();
        setItemId(id);
        setShowDelete(true);
    };

    const deleteItem = async () => {
        if (itemId) {
            deleteMutation.mutate({ id: itemId });
        }
    };

    return (
        <>
            <Helmet>
                <title>Elementor Components</title>
            </Helmet>
            <ContentHeader
                title="Elementor Components"
                contextMenu={[
                    {
                        text: 'Add Elementor Component',
                        to: '/component/add',
                        icon: 'PLUS',
                    },
                ]}
            />
            <div className="content-body">
                <div className="col-12">
                    <SearchComponentForm isLoading={isLoading || isRefetching} />
                    {(isLoading || isRefetching) && <Spinner />}
                    {!isLoading && !isRefetching && (
                        <div className="card">
                            <ListComponent
                                items={data?.template_component_list.data ?? []}
                                paging={convertPaging<Component, SearchComponentParam>(
                                    data?.template_component_list,
                                    paramConfig
                                )}
                                handleDelete={handleDelete}
                            />
                            <PaginationTable
                                countItem={data?.template_component_list.totalCount}
                                totalPage={data?.template_component_list.totalPages}
                                currentPage={data?.template_component_list.currentPage}
                                handlePageChange={handlePageChange}
                            />
                        </div>
                    )}
                    <ModalConfirm
                        show={showDelete}
                        text={t('confirm.delete', { data: 'elementor component' })}
                        btnDisabled={deleteMutation.isPending}
                        changeShow={(s: boolean) => setShowDelete(s)}
                        submitAction={deleteItem}
                    />
                </div>
            </div>
        </>
    );
}
