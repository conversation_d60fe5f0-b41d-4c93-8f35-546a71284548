import { BaseModel, DataList } from './common';

export default interface MasterTag extends BaseModel {
    name: string;
    type: string;
    display_name: string;
    tool_tip: string;
}

export interface MasterTagQuery {
    master_tags_list: DataList<MasterTag>;
}

export interface MasterTagImportResult {
    totalProcessed: number;
    created: number;
    updated: number;
    errors: string[];
    fileName: string;
    filePath: string;
}
