import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import UpdateButton from 'components/partials/UpdateButton';
import UploadImage from 'components/partials/UploadImage';
import UploadFile from 'components/partials/UploadFile';
import { DEFAULT_VALUE, ERROR_MESSAGE, QUERY_KEY, REACT_WP_URL, SUCCESS_MESSAGE, W_TOKEN_KEY } from 'constants/common';
import { useEffect, useMemo, useState, useRef, useCallback } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import Select from 'react-select';
import FileService from 'services/FileService';
import { INDUSTRY_LIST } from 'services/IndustryService';
import { TEMPLATE_CREATE, TEMPLATE_UPDATE } from 'services/TemplateService';
import { ItemStatus, ItemStatusNames, SelectOption } from 'types/common/Item';
import { isValidImageFile, selectItem, showToast } from 'utils/common';
import * as yup from 'yup';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { IndustryQuery } from '../../../types/Industry';
import Template from '../../../types/Template';
import { useTranslation } from 'react-i18next';
import UserLink from '../../../components/partials/UserLink';
import ModalConfirm from '../../../components/partials/ModalConfirm';
import { WORDPRESS_DEPLOY, WORDPRESS_DELETE, WORDPRESS_STATUS } from '../../../services/WordpressService';
import { GraphQLErrorResponse } from 'services/AxiosGraphQLClient';

interface IProps {
    id: number;
    type?: string;
    template?: Template;
}

export default function UpdateTemplateForm({ id, type, template }: Readonly<IProps>) {
    const { t } = useTranslation();
    const [image, setImage] = useState<string>(DEFAULT_VALUE.IMAGE);
    const [fileImage, setFileImage] = useState<File>();
    const [csvFile, setCsvFile] = useState<File>();
    const [templateFile, setTemplateFile] = useState<File>();
    const [sourceFile, setSourceFile] = useState<File>();
    const [databaseFile, setDatabaseFile] = useState<File>();
    const [showConfirm, setShowConfirm] = useState(false);
    const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
    const [siteStatus, setSiteStatus] = useState<'not-deployed' | 'deploying' | 'deployed' | 'init-checking'>(
        'init-checking'
    );
    const [isApproveClicked, setApproveClicked] = useState(false);

    const navigate = useNavigate();
    const statusCheckIntervalRef = useRef<number | null>(null);

    const schema = yup
        .object({
            name: yup.string().required(t('error.required')).max(255).trim(),
            industry_ids: yup.array().min(1, t('error.required')),
        })
        .required();

    const {
        register,
        handleSubmit,
        control,
        formState: { errors, isSubmitting },
        reset,
    } = useForm<Template>({
        resolver: yupResolver(schema),
        defaultValues: {
            status_id: ItemStatus.ACTIVE,
            industry_ids: [],
        },
    });

    const { data: industryData } = useGraphQLQuery<IndustryQuery>([QUERY_KEY.INDUSTRIES], INDUSTRY_LIST, {
        filters: [`status_id:=(${ItemStatus.ACTIVE})`],
        sort: 'display_order:ASC',
    });

    const industryOptions = useMemo(() => {
        const options: SelectOption[] = [];
        industryData?.industry_list.forEach((industry) => {
            options.push({
                value: industry.id!,
                label: industry.name,
            });
        });
        return options;
    }, [industryData]);

    const templateCreateMutation = useGraphQLMutation<{}, { body: Partial<Template> }>(TEMPLATE_CREATE, '', {
        onSuccess() {
            showToast(true, [SUCCESS_MESSAGE]);
            setTimeout(() => {
                navigate(`/template/list/${type}`);
            }, 2000);
        },
    });

    const templateUpdateMutation = useGraphQLMutation<{}, { id: number; body: Partial<Template> }>(
        TEMPLATE_UPDATE,
        '',
        {
            onSuccess() {
                showToast(true, [SUCCESS_MESSAGE]);
                setTimeout(() => {
                    navigate(`/template/list/${type}`);
                }, 2000);
            },
        }
    );

    const wordpressDeleteMutation = useGraphQLMutation(WORDPRESS_DELETE, '', {
        onSuccess() {
            showToast(true, ['WordPress site deleted successfully']);
            setSiteStatus('not-deployed');
        },
        onError() {
            showToast(false, ['Failed to delete WordPress site']);
        },
        customHeaders: {
            Authorization: `Bearer ${W_TOKEN_KEY}`,
        },
    });

    const wordpressDeployMutation = useGraphQLMutation(WORDPRESS_DEPLOY, '', {
        onSuccess() {
            showToast(true, ['WordPress deployment started successfully']);
            setSiteStatus('deploying');
            setApproveClicked(false);
            checkSiteStatus();
        },
        onError() {
            showToast(false, ['Failed to start WordPress deployment']);
            setApproveClicked(false);
        },
        customHeaders: {
            Authorization: `Bearer ${W_TOKEN_KEY}`,
        },
    });

    interface WordpressStatusResponse extends Omit<GraphQLErrorResponse, 'data'> {
        wordpress_status: {
            domain: string;
            status: string;
            success: boolean;
            ip: string;
            wp_password?: string;
            logs: {
                _id: string;
                domain: string;
                timestamp: string;
                step: string;
                success: boolean;
                ip?: string;
            }[];
        };
    }
    const {
        data: wordpressStatusData,
        refetch: refetchWordpressStatus,
        error: wordpressStatusError,
        isError,
    } = useGraphQLQuery<WordpressStatusResponse>(
        ['wordpress-status', template?.id],
        WORDPRESS_STATUS,
        {
            domain: template?.id ? `template-${template.id}.${REACT_WP_URL}` : '',
        },
        '',
        {
            enabled: !!template?.id && !template?.is_kit,
            refetchInterval: siteStatus === 'deploying' ? 10000 : false,
            retry: 3,
        },
        false,
        {
            Authorization: `Bearer ${W_TOKEN_KEY}`,
        }
    );
    useEffect(() => {
        const wordpressStatusErrorAny: unknown = wordpressStatusError;

        if (wordpressStatusErrorAny && isError) {
            setSiteStatus('not-deployed');
            setApproveClicked(false); // Reset approve clicked state on error
        } else if (wordpressStatusData) {
            const data = wordpressStatusData.wordpress_status;

            if (
                data.logs.find(
                    (log) => (log.step === 'deleted_instance' || log.step === 'deployment_failed') && log.success
                )
            ) {
                setSiteStatus('not-deployed');
                setApproveClicked(false);
            } else {
                if (data.status && Boolean(data.wp_password)) {
                    const previousStatus = siteStatus;
                    setSiteStatus('deployed');

                    if (isApproveClicked && previousStatus !== 'deployed' && template?.id && template?.designer?.id) {
                        const domain = `template-${template.id}.${REACT_WP_URL}`;
                        const approveUrl = `https://${domain}?action=approve&template_id=${template.id}&user_id=${template.designer.id}&bypass_token=ASiOTwM9W9ToZ0zOSDsMF1OQYUmRumys`;
                        window.open(approveUrl, '_blank');
                    }

                    setApproveClicked(false); // Reset when deployment is complete
                } else {
                    setSiteStatus('deploying');
                }
            }
        }
    }, [wordpressStatusData, wordpressStatusError, isError, isApproveClicked, siteStatus, template]);

    const onChangeFile = (file: File) => {
        if (!isValidImageFile(file)) {
            showToast(false, ['Please choose a valid image file']);
            return;
        }
        setFileImage(file);
        setImage(URL.createObjectURL(file));
    };

    const handleUploadImage = async () => {
        let imageId = 0;
        if (fileImage) {
            const results = await FileService.upload(fileImage);
            if (results.upload) {
                imageId = results.upload.id;
                setImage(results.upload.url);
            } else {
                showToast(false, [ERROR_MESSAGE]);
            }
            setFileImage(undefined);
        }
        return imageId;
    };

    const handleUploadFile = async (file: File | undefined) => {
        if (!file) return null;
        const results = await FileService.upload(file);
        if (results.upload) {
            return results.upload.id;
        }
        showToast(false, [ERROR_MESSAGE]);
        return null;
    };

    const checkSiteStatus = useCallback(() => {
        if (template?.id && !template.is_kit) {
            refetchWordpressStatus();
        }
    }, [template?.id, template?.is_kit, refetchWordpressStatus]);

    useEffect(() => {
        if (template) {
            reset({
                ...template,
                industry_ids: template.industries?.map((industry) => industry.id) ?? [],
            });
            if (template.image?.file_url) {
                setImage(template.image.file_url);
            }
            checkSiteStatus();
        }
    }, [template, reset, checkSiteStatus]);

    const deploySite = () => {
        setShowConfirm(false);
        setApproveClicked(true);

        if (template?.id) {
            const domain = `template-${template.id}.${REACT_WP_URL}`;
            wordpressDeployMutation.mutate({
                template_id: template.id,
                domain: domain,
            });
        }
    };

    const deleteSite = () => {
        setShowDeleteConfirm(true);
    };

    const confirmDeleteSite = () => {
        setShowDeleteConfirm(false);
        if (template?.id) {
            wordpressDeleteMutation.mutate({
                domain: `template-${template.id}.${REACT_WP_URL}`,
            });
            setSiteStatus('not-deployed');
        }
    };

    const onSubmit = async (data: Template) => {
        let imageId = 0;
        if (fileImage) {
            imageId = await handleUploadImage();
        }
        let csvFileId = null;
        if (csvFile) {
            csvFileId = await handleUploadFile(csvFile);
        }

        let templateFileId = null;
        if (templateFile) {
            templateFileId = await handleUploadFile(templateFile);
        }

        if (!templateFileId && !data.template_file_id && data.is_kit) {
            showToast(false, ['Please upload file']);
            return;
        }

        let sourceFileId = null;
        if (sourceFile) {
            sourceFileId = await handleUploadFile(sourceFile);
        }

        let databaseFileId = null;
        if (databaseFile) {
            databaseFileId = await handleUploadFile(databaseFile);
        }

        const formData = {
            ...data,
            id: undefined,
            image: undefined,
            industries: undefined,
            csvFile: undefined,
            codeFile: undefined,
            templateFile: undefined,
            sqlFile: undefined,
            designer: undefined,
            is_kit: type === 'kit',
            image_id:
                imageId > 0 ? imageId : data.image_id && !isNaN(Number(data.image_id)) ? Number(data.image_id) : null,
            csv_file_id: csvFileId ?? data.csv_file_id,
            template_file_id: templateFileId ?? data.template_file_id,
            code_file_id: sourceFileId ?? data.code_file_id,
            sql_file_id: databaseFileId ?? data.sql_file_id,
        };
        if (id > 0) {
            templateUpdateMutation.mutate({
                id,
                body: formData,
            });
        } else {
            templateCreateMutation.mutate({
                body: formData,
            });
        }
    };

    useEffect(
        () => () => {
            if (statusCheckIntervalRef.current) {
                clearInterval(statusCheckIntervalRef.current);
            }
        },
        []
    );

    return (
        <>
            <div className="card">
                <div className="card-header border-bottom">
                    <h4 className="card-title">{id > 0 ? 'Edit Template' : 'Add Template Kit'}</h4>
                    {template && !template.is_kit && (
                        <div>
                            {template.info?.domain && template.status_id === ItemStatus.PENDING && (
                                <>
                                    <a
                                        href={`https://${template.info.domain}?action=approve&template_id=${template.id}&user_id=${template.designer_id}&bypass_token=${W_TOKEN_KEY}`}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="btn btn-sm btn-outline-primary waves-effect waves-float waves-light mt-1 ms-2"
                                    >
                                        Review Template
                                    </a>
                                    {/*<button*/}
                                    {/*    type="button"*/}
                                    {/*    className="btn btn-sm btn-outline-danger waves-effect waves-float waves-light mt-1 ms-2"*/}
                                    {/*    onClick={deleteSite}*/}
                                    {/*>*/}
                                    {/*    Delete site*/}
                                    {/*</button>*/}
                                </>
                            )}
                        </div>
                    )}
                </div>
                <div className="card-body py-2 my-25">
                    <form className="validate-form pt-50" onSubmit={handleSubmit(onSubmit)}>
                        <div className="row">
                            <div className="col-12 col-sm-6">
                                <UploadImage id={1} image={image} label="Upload Image" onChangeFile={onChangeFile} />
                            </div>
                            <div className="col-12 col-sm-6"></div>
                            <div className="col-12 col-sm-6 mb-1">
                                <label className="form-label">
                                    Name <span className="error">*</span>
                                </label>
                                <input
                                    {...register('name')}
                                    type="text"
                                    className={classNames('form-control', {
                                        'is-invalid': Boolean(errors.name?.message),
                                    })}
                                />
                                <span className="error">{errors.name?.message}</span>
                            </div>
                            <div className="col-12 col-sm-6 mb-1">
                                <label className="form-label">Status</label>
                                <select {...register('status_id', { valueAsNumber: true })} className="form-select">
                                    {selectItem(ItemStatusNames, t, true)}
                                </select>
                            </div>
                            <div className="col-12 col-sm-6 mb-1">
                                <label className="form-label">
                                    Industries <span className="error">*</span>
                                </label>
                                <Controller
                                    name="industry_ids"
                                    control={control}
                                    rules={{ required: true }}
                                    render={({ field }) => (
                                        <Select
                                            {...field}
                                            options={industryOptions}
                                            className="react-select"
                                            classNamePrefix="select"
                                            isMulti
                                            value={industryOptions.filter((option) =>
                                                field.value?.includes(option.value as number)
                                            )}
                                            onChange={(options) =>
                                                field.onChange(options.map((option) => option.value as number))
                                            }
                                        />
                                    )}
                                />
                                <span className="error">{errors.industry_ids?.message}</span>
                            </div>
                            <div className="col-12 col-sm-6 mb-1">
                                {template && !template.is_kit && template.designer && (
                                    <>
                                        <label className="form-label">Designer</label>
                                        <p style={{ lineHeight: '42px' }}>
                                            <UserLink
                                                to={`/user/edit/${template.designer.id}`}
                                                avatar={template.designer.avatar?.file_url}
                                                name={template.designer.full_name}
                                            />
                                        </p>
                                    </>
                                )}
                            </div>
                            <div className="col-12 mb-1">
                                <div className="form-check">
                                    <label className="form-check-label" htmlFor="is_multiple">
                                        Multiple Page
                                    </label>
                                    <input
                                        type="checkbox"
                                        className="form-check-input"
                                        {...register('is_multiple')}
                                        id="is_multiple"
                                    />
                                </div>
                            </div>
                            {template && !template.is_kit && template.info?.domain && (
                                <div className="col-12 mb-1">
                                    <label className="form-check-label">
                                        URL:{' '}
                                        <a
                                            href={`https://${template.info?.domain}`}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                        >
                                            {template.info?.domain}
                                        </a>
                                    </label>
                                </div>
                            )}
                            <div className="col-12 mb-1">
                                <label className="form-label">Description</label>
                                <textarea
                                    {...register('desc')}
                                    className={classNames('form-control', {
                                        'is-invalid': Boolean(errors.desc?.message),
                                    })}
                                    rows={2}
                                />
                                {errors.desc?.message && <div className="error">{errors.desc.message}</div>}
                            </div>

                            {/* File Upload Section - more user friendly */}
                            <div className="col-12 ">
                                <div className="card" style={{ background: '#f8f9fa', border: 'none' }}>
                                    <div className="card-body pb-2 pt-2">
                                        <h6 className="mb-1" style={{ fontWeight: 600 }}>
                                            Additional Files
                                        </h6>
                                        <div className="row">
                                            {/*<div className="col-6">*/}
                                            {/*    <UploadFile*/}
                                            {/*        fileNameInit={template?.csvFile?.file_name}*/}
                                            {/*        fileUrl={template?.csvFile?.file_url}*/}
                                            {/*        id={2}*/}
                                            {/*        label="Upload CSV File (csv)"*/}
                                            {/*        onChangeFile={setCsvFile}*/}
                                            {/*        accept=".csv"*/}
                                            {/*    />*/}
                                            {/*    <small className="text-muted">Allowed: .csv</small>*/}
                                            {/*</div>*/}
                                            <div className="col-6">
                                                <UploadFile
                                                    fileNameInit={template?.templateFile?.file_name}
                                                    fileUrl={template?.templateFile?.file_url}
                                                    id={3}
                                                    label="Upload File"
                                                    onChangeFile={setTemplateFile}
                                                    accept=".zip"
                                                    hideInput={!!template}
                                                />
                                                <small className="text-muted">Allowed: .zip</small>
                                            </div>
                                            {/*<div className="col-6">*/}
                                            {/*    <UploadFile*/}
                                            {/*        fileNameInit={template?.codeFile?.file_name}*/}
                                            {/*        fileUrl={template?.codeFile?.file_url}*/}
                                            {/*        id={4}*/}
                                            {/*        label="Upload Source (zip)"*/}
                                            {/*        onChangeFile={setSourceFile}*/}
                                            {/*        accept=".zip"*/}
                                            {/*    />*/}
                                            {/*    <small className="text-muted">Allowed: .zip</small>*/}
                                            {/*</div>*/}
                                            {/*<div className="col-6">*/}
                                            {/*    <UploadFile*/}
                                            {/*        fileNameInit={template?.sqlFile?.file_name}*/}
                                            {/*        fileUrl={template?.sqlFile?.file_url}*/}
                                            {/*        id={5}*/}
                                            {/*        label="Upload Database (zip or sql)"*/}
                                            {/*        onChangeFile={setDatabaseFile}*/}
                                            {/*        accept=".zip,.sql"*/}
                                            {/*    />*/}
                                            {/*    <small className="text-muted">Allowed: .zip, .sql</small>*/}
                                            {/*</div>*/}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="col-12 mt-2">
                                <UpdateButton btnText={t('update')} isLoading={isSubmitting} btnClass={['mt-1']} />
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <ModalConfirm
                show={showConfirm}
                text="Are you sure you want to approve and deploy this template to WordPress?"
                btnDisabled={wordpressDeployMutation.isPending}
                changeShow={(s: boolean) => setShowConfirm(s)}
                submitAction={deploySite}
            />
            <ModalConfirm
                show={showDeleteConfirm}
                text="Are you sure you want to delete this WordPress site? This action cannot be undone."
                btnDisabled={false}
                changeShow={(s: boolean) => setShowDeleteConfirm(s)}
                submitAction={confirmDeleteSite}
            />
        </>
    );
}
