import { gql } from 'graphql-request';

export const TICKET_LIST = gql`
    query Ticket_list($page: Int!, $limit: Int!, $filters: [String!], $sort: String, $search: String) {
        tickets_list(input: { page: $page, limit: $limit, filters: $filters, sort: $sort, search: $search }) {
            totalCount
            totalPages
            currentPage
            data {
                id
                title
                feedback
                content
                customer {
                    id
                    full_name
                }
                type_id
                status_id
            }
        }
    }
`;

export const TICKET_UPDATE = gql`
    mutation Ticket_update($id: Int!, $input: TicketUpdateInputDto!) {
        tickets_update(id: $id, input: $input) {
            id
        }
    }
`;
