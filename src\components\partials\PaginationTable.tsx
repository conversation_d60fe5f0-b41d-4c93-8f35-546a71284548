import { Pagination } from '@mui/material';
import { useTranslation } from 'react-i18next';

interface IProps {
    countItem: number | undefined;
    totalPage: number | undefined;
    currentPage: number | undefined;
    handlePageChange: (event: React.ChangeEvent<unknown>, page: number) => void;
}

export default function PaginationTable({ countItem, totalPage, currentPage, handlePageChange }: Readonly<IProps>) {
    const { t } = useTranslation();

    return countItem && totalPage ? (
        <div className="card-footer">
            <p className="text-muted float-start">
                {t('totalRecords')}: {countItem}
            </p>
            <div className="float-end">
                {totalPage > 1 && (
                    <Pagination
                        count={totalPage}
                        page={currentPage}
                        color="primary"
                        size="small"
                        onChange={handlePageChange}
                    />
                )}
            </div>
        </div>
    ) : (
        <></>
    );
}
