import Spinner from 'components/partials/Spinner';
import { PAGINATION, QUERY_KEY } from 'constants/common';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import { Helmet } from 'react-helmet-async';
import ListWebsite from '../components/ListWebsite';
import { WEBSITE_LIST } from 'services/WebsiteService';
import Website, { SearchWebsiteParam, websiteFilterConfig, WebsiteQuery } from 'types/Website';
import { convertPaging, generateFilters } from '../../../utils/common';
import useQueryParams from '../../../hooks/useQueryParams';
import omitBy from 'lodash/omitBy';
import isUndefined from 'lodash/isUndefined';
import { keepPreviousData } from '@tanstack/react-query';
import ContentHeader from 'components/partials/ContentHeader';
import PaginationTable from '../../../components/partials/PaginationTable';
import SearchWebsiteForm from '../components/SearchWebsiteForm';
import { useState } from 'react';

export default function WebsiteList() {
    const { queryParams, setQueryParams } = useQueryParams<SearchWebsiteParam>();
    
    const paramConfig: SearchWebsiteParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            search: queryParams.search,
            status_id: queryParams.status_id,
            customer_id: queryParams.customer_id,
            template_id: queryParams.template_id,
        },
        isUndefined
    );

    const { search, limit, page, ...dataParamConfig } = paramConfig;
    const filters = generateFilters(dataParamConfig, websiteFilterConfig);

    const { data, isLoading, isRefetching, refetch } = useGraphQLQuery<WebsiteQuery>(
        [QUERY_KEY.WEBSITES, paramConfig, filters],
        WEBSITE_LIST,
        {
            page: Number(page),
            limit: limit,
            filters: filters.length > 0 ? filters : undefined,
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    return (
        <>
            <Helmet>
                <title>Websites</title>
            </Helmet>
            <ContentHeader title="Websites" />
            <div className="content-body">
                <div className="col-12">
                    <SearchWebsiteForm isLoading={isLoading || isRefetching} />
                    {(isLoading || isRefetching) && <Spinner />}
                    {!isLoading && !isRefetching && (
                        <div className="card">
                            <ListWebsite
                                items={data?.websites_list.data ?? []}
                                paging={convertPaging<Website, SearchWebsiteParam>(data?.websites_list, paramConfig)}
                                refetch={refetch}
                            />
                            <PaginationTable
                                countItem={data?.websites_list.totalCount}
                                totalPage={data?.websites_list.totalPages}
                                currentPage={data?.websites_list.currentPage}
                                handlePageChange={handlePageChange}
                            />
                        </div>
                    )}
                </div>
            </div>
        </>
    );
}
