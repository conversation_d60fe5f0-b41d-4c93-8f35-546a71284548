import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import UpdateButton from 'components/partials/UpdateButton';
import OTPForm from 'features/auth/components/OTPForm';
import { useGraphQLMutation } from 'hooks/useGraphQLMutation';
import { useState } from 'react';
import { Eye, EyeOff } from 'react-feather';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { LOGIN } from 'services/UserService';
import { showToast } from 'utils/common';
import * as yup from 'yup';
import User, { LoginData, UserQuery, UserRole } from '../../../types/User';
import { OPERATION_NAME, SUCCESS_MESSAGE } from '../../../constants/common';

interface IProps {
    url: string;
}

export default function LoginForm({ url }: Readonly<IProps>) {
    const [showPass, setShowPass] = useState(false);
    const [user, setUser] = useState<User>();
    const { t } = useTranslation();
    const schema = yup
        .object({
            email: yup.string().required(t('error.required')).email(t('error.email')).trim(),
            password: yup.string().required(t('error.required')).trim().min(3, t('error.passwordLength3')),
        })
        .required();
    const {
        register,
        handleSubmit,
        formState: { isSubmitting, errors },
    } = useForm<LoginData>({
        resolver: yupResolver(schema),
    });

    const { mutate } = useGraphQLMutation<UserQuery, LoginData>(LOGIN, OPERATION_NAME.LOGIN, {
        onSuccess: () => {
            showToast(true, [SUCCESS_MESSAGE]);
            setTimeout(() => {
                window.location.href = url ?? '/dashboard';
            }, 2000);
        },
    });

    const onSubmit = async (data: LoginData) => {
        data.role_id = UserRole.ADMIN;
        mutate(data);
    };

    return (
        <>
            {!user ? (
                <form className="auth-login-form mt-2" onSubmit={handleSubmit(onSubmit)}>
                    <div className="mb-1">
                        <label className="form-label">{t('email')}</label>
                        <input
                            {...register('email')}
                            className={classNames('form-control', { error: Boolean(errors.email?.message) })}
                            type="text"
                        />
                        <span className="error">{errors.email?.message}</span>
                    </div>
                    <div className="mb-1">
                        <div className="d-flex justify-content-between">
                            <label className="form-label">{t('password')}</label>
                            <Link to="/forgot">
                                <small>{t('forgotPassword')}</small>
                            </Link>
                        </div>
                        <div className="input-group input-group-merge form-password-toggle">
                            <input
                                {...register('password')}
                                className={classNames('form-control', 'form-control-merge', {
                                    error: Boolean(errors.password?.message),
                                })}
                                type={showPass ? 'text' : 'password'}
                                placeholder="············"
                            />
                            <span
                                className="input-group-text cursor-pointer"
                                onClick={() => setShowPass((prevShowPass) => !prevShowPass)}
                                style={{
                                    borderColor: errors.password?.message ? '#ea5455' : '#d8d6de',
                                }}
                            >
                                {showPass ? <EyeOff size={14} /> : <Eye size={14} />}
                            </span>
                        </div>
                        <span className="error">{errors.password?.message}</span>
                    </div>
                    <UpdateButton
                        isLoading={isSubmitting}
                        btnClass={['w-100']}
                        btnText={t('login')}
                        hasDivWrap={false}
                    />
                </form>
            ) : (
                <OTPForm user={user} url={url} />
            )}
        </>
    );
}
