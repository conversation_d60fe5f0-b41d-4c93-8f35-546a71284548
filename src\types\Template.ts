import { FILTER_CONDITIONS } from '../constants/common';
import Industry from './Industry';
import User from './User';
import { baseFilterConfig, BaseModel, BaseSearch, DataList, FilterConfig } from './common';
import { ItemFile, ItemParam, ItemStatus } from './common/Item';

export enum TemplateType {
    KIT = 1,
    LIBRARY = 0,
}

export default interface Template extends BaseModel {
    name: string;
    desc?: string;
    status_id: ItemStatus;
    designer_id: number;
    image_id?: number | null;
    csv_file_id?: number | null;
    code_file_id?: number | null;
    template_file_id?: number | null;
    sql_file_id?: number;
    is_multiple: boolean;
    info?: {
        domain: string;
    };
    approved_date?: string;
    is_kit: boolean;
    designer?: User;
    image?: ItemFile;
    industries?: Industry[];
    industry_ids?: number[];
    csvFile?: ItemFile;
    codeFile?: ItemFile;
    templateFile?: ItemFile;
    sqlFile?: ItemFile;
}

export interface SearchTemplate extends BaseSearch {
    designer_id?: string;
    industry_id?: string;
}

export type SearchTemplateParam = {
    [key in keyof SearchTemplate]: string;
};

export interface TemplateQuery {
    template_list: DataList<Template>;
}

export interface TemplateViewResponse {
    template_view: Template;
}

export const templateFilterConfig: FilterConfig = {
    ...baseFilterConfig,
    designer_id: { key: 'designer_id', operator: FILTER_CONDITIONS.IN },
    industry_id: { key: 'industries.id', operator: FILTER_CONDITIONS.IN },
    is_multiple: { key: 'is_multiple', operator: FILTER_CONDITIONS.EQUAL },
    is_kit: { key: 'is_kit', operator: FILTER_CONDITIONS.EQUAL },
};

export const TemplateTypeNames: ItemParam[] = [
    { id: TemplateType.KIT, name: 'kit' },
    { id: TemplateType.LIBRARY, name: 'library' },
];
