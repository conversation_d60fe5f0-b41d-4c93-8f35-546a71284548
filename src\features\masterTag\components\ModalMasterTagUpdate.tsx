import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import UpdateButton from 'components/partials/UpdateButton';
import { useEffect, useLayoutEffect } from 'react';
import { useForm } from 'react-hook-form';
import MasterTag from 'types/MasterTag';
import { toggleModalOpen } from 'utils/common';
import * as yup from 'yup';
import { useTranslation } from 'react-i18next';

interface IProps {
    show: boolean;
    masterTag?: MasterTag;
    isLoading?: boolean;
    changeShow: (s: boolean) => void;
    submitAction: (data: MasterTag) => void;
}

export default function ModalMasterTagUpdate({
    show,
    masterTag,
    isLoading,
    changeShow,
    submitAction,
}: Readonly<IProps>) {
    const { t } = useTranslation();
    useLayoutEffect(() => toggleModalOpen(show), [show]);

    const schema = yup
        .object({
            name: yup.string().required(t('error.required')).trim(),
            type: yup.string().required(t('error.required')).trim(),
            display_name: yup.string().required(t('error.required')).trim(),
            tool_tip: yup.string().required(t('error.required')).trim(),
        })
        .required();

    const {
        register,
        handleSubmit,
        reset,
        formState: { errors },
    } = useForm<MasterTag>({
        resolver: yupResolver(schema),
    });

    useEffect(() => {
        if (masterTag && show) {
            reset(masterTag);
        } else {
            reset({
                name: '',
                type: 'text',
                display_name: '',
                tool_tip: '',
            });
        }
    }, [masterTag, show, reset]);

    return (
        <>
            <div
                className={classNames('modal fade text-start modal-primary', { show })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-dialog-centered">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">{masterTag ? 'Edit Master Tag' : 'Add Master Tag'}</h5>
                            <button type="button" className="btn-close" onClick={() => changeShow(false)} />
                        </div>
                        <form className="validate-form pt-50" onSubmit={handleSubmit(submitAction)}>
                            <div className="modal-body">
                                <div className="row">
                                    <div className="col-12 mb-1">
                                        <label className="form-label">
                                            Tag name <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('name')}
                                            type="text"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.name?.message),
                                            })}
                                        />
                                        <span className="error">{errors.name?.message}</span>
                                    </div>
                                    <div className="col-12 mb-1">
                                        <label className="form-label">
                                            Tag Type <span className="error">*</span>
                                        </label>
                                        <select {...register('type')} className="form-select">
                                            {['image', 'button', 'text', 'link', 'iframe', 'video', 'icon'].map(
                                                (type) => (
                                                    <option key={type} value={type}>
                                                        {type}
                                                    </option>
                                                )
                                            )}
                                        </select>
                                    </div>
                                    <div className="col-12 mb-1">
                                        <label className="form-label">
                                            Display Name <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('display_name')}
                                            type="text"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.display_name?.message),
                                            })}
                                        />
                                        <span className="error">{errors.display_name?.message}</span>
                                    </div>
                                    <div className="col-12 mb-1">
                                        <label className="form-label">
                                            Tool tip <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('tool_tip')}
                                            type="text"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.tool_tip?.message),
                                            })}
                                        />
                                        <span className="error">{errors.tool_tip?.message}</span>
                                    </div>
                                </div>
                            </div>
                            <div className="modal-footer">
                                <UpdateButton btnText={t('update')} isLoading={isLoading} hasDivWrap={false} />
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}
        </>
    );
}
