import { keepPreviousData } from '@tanstack/react-query';
import ContentHeader from 'components/partials/ContentHeader';
import ModalConfirm from 'components/partials/ModalConfirm';
import PaginationTable from 'components/partials/PaginationTable';
import Spinner from 'components/partials/Spinner';
import { PAGINATION, QUERY_KEY, SUCCESS_MESSAGE } from 'constants/common';
import useQueryParams from 'hooks/useQueryParams';
import isUndefined from 'lodash/isUndefined';
import omitBy from 'lodash/omitBy';
import { useMemo, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuthStore } from 'stores/authStore';
import User, { SearchUserParam, UserListQuery, UserRoleNames, userFilterConfig } from 'types/User';
import { convertPaging, generateFilters, getFieldInArrayObject, showToast } from 'utils/common';
import ListUser from '../components/ListUser';
import SearchUserForm from '../components/SearchUserForm';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { USER_DELETE, USER_LIST } from '../../../services/UserService';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import ModalSmtpConfig from '../components/ModalStmpConfig';

export default function UserList() {
    const user = useAuthStore((state) => state.user);
    const { type } = useParams();
    const navigate = useNavigate();
    const { t } = useTranslation();
    const roleId = useMemo(() => +getFieldInArrayObject(UserRoleNames, type ?? '', 'id', '0', 'name'), [type]);
    if (roleId === 0) {
        navigate('/not-found');
    }

    const { queryParams, setQueryParams } = useQueryParams<SearchUserParam>();
    const paramConfig: SearchUserParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            search: queryParams.search,
            status_id: queryParams.status_id,
            role_id: roleId.toString(),
        },
        isUndefined
    );

    const { search, limit, page, ...dataParamConfig } = paramConfig;
    const filters = generateFilters(dataParamConfig, userFilterConfig);

    const [showDelete, setShowDelete] = useState(false);
    const [showSmtp, setShowSmtp] = useState(false);
    const [itemId, setItemId] = useState(0);

    const { data, isLoading, isRefetching, refetch } = useGraphQLQuery<UserListQuery>(
        [QUERY_KEY.USERS, paramConfig, user, filters],
        USER_LIST,
        {
            page: Number(page),
            limit: limit,
            search,
            filters: filters.length > 0 ? filters : undefined,
            // sort: paramConfig.sort,
        },
        '',
        {
            enabled: !!roleId && !!user,
            placeholderData: keepPreviousData,
        }
    );

    const deleteMutation = useGraphQLMutation(USER_DELETE, '', {
        onSuccess: () => {
            showToast(true, [SUCCESS_MESSAGE]);
            setShowDelete(false);
            setItemId(0);
            refetch();
        },
    });

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    const handleDelete = (id: number) => {
        deleteMutation.reset();
        setItemId(id);
        setShowDelete(true);
    };

    const handleSmtp = (id: number) => {
        setItemId(id);
        setShowSmtp(true);
    };

    const deleteItem = async () => {
        if (itemId) {
            deleteMutation.mutate({ id: itemId });
        }
    };

    return (
        <>
            <Helmet>
                <title>{t(`${type}.multiple`)}</title>
            </Helmet>
            <ContentHeader
                title={t(`${type}.multiple`)}
                contextMenu={[
                    {
                        text: t(`${type}.add`),
                        to: `/user/add/${type}`,
                        icon: 'PLUS',
                    },
                ]}
            />
            <div className="content-body">
                <div className="col-12">
                    <SearchUserForm isLoading={isLoading || isRefetching} />
                    {(isLoading || isRefetching) && <Spinner />}
                    {!isLoading && !isRefetching && (
                        <div className="card">
                            <ListUser
                                items={data?.users_list.data ?? []}
                                paging={convertPaging<User, SearchUserParam>(data?.users_list, paramConfig)}
                                handleDelete={handleDelete}
                                currenUser={user}
                                roleId={roleId}
                                handleSmtp={handleSmtp}
                            />
                            <PaginationTable
                                countItem={data?.users_list.totalCount}
                                totalPage={data?.users_list.totalPages}
                                currentPage={data?.users_list.currentPage}
                                handlePageChange={handlePageChange}
                            />
                        </div>
                    )}
                    <ModalConfirm
                        show={showDelete}
                        text={t('confirm.delete', { data: 'user' })}
                        btnDisabled={deleteMutation.isPending}
                        changeShow={(s: boolean) => setShowDelete(s)}
                        submitAction={deleteItem}
                    />
                    <ModalSmtpConfig show={showSmtp} userId={itemId} changeShow={(s: boolean) => setShowSmtp(s)} />
                </div>
            </div>
        </>
    );
}
