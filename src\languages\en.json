{"siteName": "IPT", "error": {"common": "An error occurred during execution", "required": "This field is required", "fileRequired": "Please choose zip file", "passwordLength": "Use 12 characters or more for your password.", "passwordLength3": "Use 3 characters or more for your password.", "passwordUppercase": "The password should contain at least one uppercase letter.", "passwordLowercase": "The password should contain at least one lowercase letter.", "passwordNumber": "The password should contain at least one numeric character.", "passwordSpecial": "The password should contain at least one special character.", "passwordNotSame": "Passwords are not the same", "email": "Invalid email address", "number": "This field must be numeric", "min_0": "The smallest value is 0", "greaterThan_0": "The value need to greater than 0", "min_1": "The smallest value is 1", "max_30": "The largest value is 30", "max_99": "The largest value is 99", "max_port": "The largest value is 65535", "chooseProduct": "Please choose at least one product", "chooseImage": "Invalid file, please select another image file", "barcode": "Invalid barcode", "quantity": "Invalid quantity", "confirmEmailNotSame": "The email addresses do not match."}, "constants": {"pending": "Pending", "active": "Active", "inactive": "In Active", "male": "Male", "female": "Female", "other": "Other", "yes": "Yes", "no": "No", "locked": "Locked", "trial": "Trial", "basic": "Basic", "premium": "Premium", "website": "Website", "domain": "Domain", "paid": "Paid", "unpaid": "Unpaid", "day": "Day", "month": "Month", "websiteDown": "Website Down", "processing": "Processing", "finish": "Finish"}, "loginAdminDesc": "Log in to the administration system", "phoneNumber": "Phone Number", "password": "Password", "forgotPassword": "Forgot Password", "login": "<PERSON><PERSON>", "forgotPasswordDesc": "Please enter Email to retrieve your password", "backToLogin": "Back to Login", "email": "Email", "tel": "Tel", "send": "Send", "security": "Security", "enable": "Enable", "disable": "Disable", "changePassword": "Change Password", "resetPassword": "Reset Password", "resetPasswordDesc": "Re-enter the new password to access the system", "oldPassword": "Old Password", "newPassword": "New password", "rePassword": "Re-enter the password", "website": "Website", "comment": "Comment", "ipAddress": "IP Address", "notifications": "Notifications", "notFound": "Not Found", "notFoundDesc": "The requested resource was not found on the server", "backToDashboard": "Back to Dashboard", "NumberWebsites": "Number of websites", "agree": "Agree", "add": "Add", "edit": "Edit", "delete": "Delete", "update": "Update", "create": "Create", "totalRecords": "Total Records", "profile": "Profile", "logout": "Logout", "dashboards": "Dashboards", "no.": "No.", "displayOrder": "Display Order", "actions": "Actions", "actionName": "Action Name", "url": "URL", "parent": "Parent", "icon": "Icon", "fullName": "Full Name", "firstName": "First Name", "lastName": "Last Name", "country": "Country", "address": "Address", "statusName": "Status", "typeName": "Type", "genderName": "Gender", "birthday": "Birthday", "verifiedEmail": "Verified email", "search": "Search", "reset": "Reset", "choose": "<PERSON><PERSON>", "keywords": "Keywords", "allowImageFormats": "Allowed formats: png, jpg, jpeg, gif, webp, bmp", "baseInformation": "Base Information", "uploadAvatar": "Upload Avatar", "uploadImage": "Upload Image", "permissions": "Permissions", "createdUser": "Created User", "createdTime": "Created Time", "sentTime": "Sent Time", "name": "Name", "domain": "Domain", "currency": "<PERSON><PERSON><PERSON><PERSON>", "code": "Code", "itemCode": "Item Code", "sku": "SKU", "type": "Type", "vendor": "<PERSON><PERSON><PERSON>", "quantityWarning": "Quantity Warning", "shopOwner": "Shop Owner", "accessToken": "Access Token", "webhookSecret": "Webhook Secret", "orderCode": "Order Code", "totalPrice": "Total Price", "price": "Price", "quantity": "Quantity", "virtualQuantity": "Virtual Quantity", "missing": "Missing", "file": "File", "createAuto": "Created auto", "style": "Style", "barcode": "Barcode", "all": "All", "printJobSheet": "Print Job Sheet", "color": "Color", "size": "Size", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "centre": "Centre", "crossCheck": "Cross Check", "item": "<PERSON><PERSON>", "totalItem": "Total Item", "poNumber": "PO Number", "date": "Date", "startDate": "From Date", "endDate": "To Date", "year": "Year", "transfer": "Transfer", "properties": "Properties", "confirmText": "Confirm", "confirm": {"delete": "Do you wish to remove this {{data}}?", "status": "Do you really want change status to {{data}}?", "createTransfer": "Do you really want to create transfer?", "startManufacturing": "Do you really want to start manufacturing?", "finishManufacture": "Do you really want to finish manufacture?", "finishPacking": "Do you really want to finish packing?"}, "scan": {"single": "<PERSON><PERSON>", "transfer": "Transfer Scan", "packing": "Packing Scan", "tipTitle": "Let's click in here to scan barcode", "tipDescription": "You will click successfully if the box is blue", "quantity": "Quantity <PERSON>an"}, "twoStepsVerification": {"title": "Two-steps verification", "desc": "Keep your account secure with authentication step", "authenticatorApps": "Authenticator Apps", "authenticatorAppDesc1": "Get codes from an app like Google Authenticator, Microsoft Authenticator, Authy or 1Password", "authenticatorAppDesc2": "Using an authenticator app like Google Authenticator, Microsoft Authenticator, Authy, or 1Password, scan the QR code. It will generate a 6 digit code for you to enter below", "authenticatorAppDesc3": "If you having trouble using the QR code, select manual entry on your app", "enterAuthenticationCode": "Enter authentication code"}, "otp": {"multiple": "OTP", "single": "OTP", "add": "OTP", "edit": "OTP"}, "group": {"multiple": "Groups", "single": "Group Name", "add": "Add Group", "edit": "Edit Group", "description": "Description"}, "admin": {"single": "Administrator", "multiple": "Administrators", "add": "Add Administrator", "edit": "Edit Administrator"}, "customer": {"multiple": "Customers", "single": "Customer", "add": "Add Customer", "edit": "Edit Customer", "details": "Customer Details"}, "designer": {"multiple": "Designers", "single": "Designer", "add": "Add Designer", "edit": "Edit Designer", "wait": "Wait Designer"}, "shop": {"multiple": "Shops", "single": "Shop", "add": "Add Shop", "edit": "Edit Shop"}, "location": {"multiple": "Locations", "single": "Location", "of": "Locations of {{data}}"}, "warehouse": {"multiple": "Warehouses", "single": "Warehouse", "add": "Add Warehouse", "edit": "Edit Warehouse", "update": "Update Warehouse", "without": "Without Warehouses"}, "product": {"multiple": "Products", "single": "Product", "sync": "Sync Product", "syncConfirm": "Do you really want to sync products from this shop?", "quantityOf": "Quantity of {{data}}", "newQuantity": "New quantity", "stockWarning": "Stocks warning"}, "design": {"multiple": "Designs", "single": "Design", "of": "Designs of {{data}}", "placement": "Placement", "file": "<PERSON>", "fileRequired": "Please choose design file", "colorRequired": "Please choose color", "placementExist": "Placement is existed", "quantity": "Designed quantity", "completed": "Completed design", "sleeve": "Sleeve", "left": "Left", "right": "Right", "digital": "Digital", "digitalAI": "Digital AI", "sizeAndPosition": "Position", "done": "Done Design", "doneAll": "Done All Design", "preset": "Preset", "presetOf": "Preset of {{data}}", "presetRequired": "Please input preset only 1 character"}, "designerSchedule": {"multiple": "Designer Holiday Schedules", "single": "Designer Holiday Schedule", "add": "Add Designer Holiday Schedule", "edit": "Edit Designer Holiday Schedule"}, "order": {"multiple": "Orders", "single": "Order", "status": "Order Status", "billingAddress": "Billing Address", "shippingAddress": "Shipping Address", "paymentGateway": "Payment Gateway", "date": "Order date", "note": "Order Notes", "summaries": "Order Summaries", "viewMap": "View Map", "count": "Order Count", "resendShip": "Resend Ship", "isDesignerDone": "Is Designer Done", "doneDesign": "Designer Done", "find": "Find Order"}, "requestOrder": {"multiple": "Request Orders", "itemPerOrder": "Item Per Order"}, "receive": {"multiple": "Receive Products", "single": "Receive Product", "add": "Add Receive Product", "edit": "View Receive Product", "create": "Create Receive Product"}, "delivery": {"multiple": "Delivery Products", "add": "Add Delivery Product", "edit": "View Delivery Product", "create": "Create Delivery Product"}, "productWeight": {"multiple": "Product Weights", "single": "Product Weight", "add": "Add Product Weight", "edit": "Edit Product Weight", "weight": "Weight (gram)", "sizeError": "Width and Height is required and need to greater than 0"}, "partnerOrderQuantity": "Partner's Order Quantity", "salesRevenue": "Sales Revenue", "goodsSold": " Quantity of Goods Sold", "countOrderByStatus": "Order's Status", "total_order": "Total Order", "total_revenue": "Total Revenue", "industry": {"single": "Industry", "multiple": "Industries"}, "kit": {"multiple": "Elementor templates"}, "library": {"multiple": "Template Library"}, "subscription_plan": {"single": "Subscription Plan", "add": "Add Subscription Plan", "edit": "View Subscription Plan", "description": "Description", "plan_type": "Plan Type", "price": "Price", "trial_days": "Trial Days", "features": "Features"}, "wordpress": {"deploying": "Deploying", "deployed": "Deployed successfully", "not_deployed": "Not Deployed", "deployment_failed": "Deployment Failed"}}